<script setup lang="ts">
import { Power } from 'lucide-vue-next'
import AppSidebar from '@/components/AppSidebar.vue'
import { SidebarInset, SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar'

const user = useSupabaseUser()
</script>

<template>
  <SidebarProvider>
    <AppSidebar />
    <SidebarInset>
      <header class="bg-sidebar flex md:hidden h-14 shrink-0 items-center justify-between gap-2">
        <div class="flex items-center gap-2 px-4">
          <SidebarTrigger class="-ml-1 lg:hidden" />
        </div>

        <AppLogo />

        <DropdownMenu v-if="user">
          <DropdownMenuTrigger as-child>
            <Button variant="ghost" size="lg" class="-mr-1">
              <Avatar class="h-8 w-8 rounded-lg">
                <AvatarImage v-if="user.avatar" :src="user.avatar" :alt="user.email" />
                <AvatarFallback class="rounded-lg"> {{ user.email?.substring(0, 2) || '' }} </AvatarFallback>
              </Avatar>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" side="top">
            <DropdownMenuItem>
              <NuxtLink to="/logout" class="grow font-display">Logout</NuxtLink>
              <Power class="ml-2 h-4 w-4" />
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
        <Button v-else as-child variant="ghost" size="lg">
          <NuxtLink to="/login" class="font-display">Login</NuxtLink>
        </Button>
      </header>

      <div class="pt-3 px-1 md:pt-3 md:px-3 xl:px-4 max-w-full">
        <slot />
      </div>
    </SidebarInset>
  </SidebarProvider>
</template>
