import { DeleteObjectCommand, DeleteObjectsCommand, S3Client } from '@aws-sdk/client-s3'
import { Upload } from '@aws-sdk/lib-storage'

const helper = {
  upload: async (userId: string, imageId: string, buffer: any, event: any) => {
    // Get runtime config for Cloudflare R2 credentials
    const config = useRuntimeConfig(event)

    // Create S3 client with Cloudflare R2 configuration
    const s3Client = new S3Client({
      endpoint: config.cloudflare.endpoint,
      region: config.cloudflare.region,
      credentials: {
        accessKeyId: config.cloudflare.accessKeyId,
        secretAccessKey: config.cloudflare.secretAccessKey
      }
    })

    // Generate unique filename to prevent collisions
    const fileName = `${userId}/${imageId}.webp`

    // Upload to Cloudflare R2
    const upload = new Upload({
      client: s3Client,
      params: {
        Bucket: config.cloudflare.bucketName,
        Key: fileName,
        Body: buffer,
        ContentType: 'image/webp'
      }
    })

    await upload.done()

    // Return success response with the file URL
    return {
      success: true,
      url: `${fileName}`,
      key: fileName
    }
  },

  delete: async (keys: string[], event: any) => {
    // Get runtime config for Cloudflare R2 credentials
    const config = useRuntimeConfig(event)

    // Create S3 client with Cloudflare R2 configuration
    const s3Client = new S3Client({
      endpoint: config.cloudflare.endpoint,
      region: config.cloudflare.region,
      credentials: {
        accessKeyId: config.cloudflare.accessKeyId,
        secretAccessKey: config.cloudflare.secretAccessKey
      }
    })

    // Delete the object from Cloudflare R2

    if (keys.length === 1) {
      const command = new DeleteObjectCommand({
        Bucket: config.cloudflare.bucketName,
        Key: keys[0]
      })

      await s3Client.send(command)
    } else {
      const command = new DeleteObjectsCommand({
        Bucket: config.cloudflare.bucketName,
        Delete: {
          Objects: keys.map((key) => ({ Key: key }))
        }
      })

      const results = await s3Client.send(command)
      if (!results || results.$metadata?.httpStatusCode !== 200 || results.Errors?.length || results.Deleted?.length !== keys.length) {
        throw new Error(`Failed to delete files`)
      }
    }

    return {
      success: true,
      message: 'Files deleted successfully'
    }
  }
}

export default helper
