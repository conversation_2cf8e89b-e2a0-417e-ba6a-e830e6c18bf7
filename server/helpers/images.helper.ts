import sharp from 'sharp'
import { encode } from 'blurhash'
import { rgbaToThumbHash } from 'thumbhash'

const defaultThumbhash = 'j+cBBQB4eYh3h3iHeHiH+PB7B5iX'

const helper = {
  computeThumbhash: async (imageData: any) => {
    try {
      const thumbImageSize = 100

      const sharpImage = sharp(imageData)
      // console.log('Thumbhash image load completed')

      const resizedImage = await sharpImage.resize(thumbImageSize, thumbImageSize, {
        fit: sharp.fit.inside
      })
      // console.log('Thumbhash image resize completed')

      const { data, info } = await resizedImage.ensureAlpha().raw().toBuffer({
        resolveWithObject: true
      })
      // console.log('Thumbhash image to buffer completed')

      const thumbhash = rgbaToThumbHash(info.width, info.height, new Uint8ClampedArray(data))
      // console.log('Thumbhash computation completed')

      const thumbBase64 = btoa(String.fromCharCode(...thumbhash)).replace(/=+$/, '')
      return thumbBase64
    } catch (error) {
      console.error('Error computing thumbhash:', error)
      return defaultThumbhash
    }
  }
}

export default helper
