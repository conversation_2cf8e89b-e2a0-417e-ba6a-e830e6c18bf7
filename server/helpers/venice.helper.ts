import { enhanceInstructions, enhanceStyles, models, VeniceModels } from '~/config/venice.config'

const veniceApiUrl = 'https://api.venice.ai/api/v1'

const textModels = {
  small: {
    name: 'qwen3-4b',
    description: 'Venice Small'
  },
  medium: {
    name: 'mistral-31-24b',
    description: 'Venice Medium'
  }
}
const generatePromptForEnhancement = (prompt: string, style?: keyof typeof enhanceStyles) => {
  if (style && enhanceStyles[style]) {
    return `The user has written the following prompt to generate an image using an AI image model:

  "${prompt}"

  Your task is to edit this prompt by creating a strong stylistic direction: ${enhanceStyles[style]}. If the original prompt already includes a style, change the wording related to the style to replace it with this one.

  Be careful to preserve the user's core ideas and elements for the prompt. Do not significantly change the subject or structure—just enhance it aesthetically with the chosen style.`
  }

  return `The user has written the following prompt to generate an image using an AI image model:

  "${prompt}"

  Your task is to enhance this prompt without changing its core meaning. Respect the user's vision, but make the result more vivid, specific, and creative. Add sensory or atmospheric details to help bring the image to life.

  Do not add or assume a visual style unless one is explicitly mentioned by the user.`
}

const helper = {
  generateImage: async (params: any, event: any) => {
    // Get runtime config for Venice API
    const config = useRuntimeConfig(event)

    if (!config.venice?.apiKey) {
      throw new Error('Venice API key not defined')
    }

    // parse user config
    const modelName: VeniceModels = params.model || 'venice-sd35'
    const modelConfig = models[modelName]

    if (!modelConfig) {
      throw new Error('Invalid model specified')
    }

    const negativePrompt = modelConfig.params?.safe_mode === false ? '' : 'nsfw nude porn sex'
    const loraStrength = modelConfig.params?.lora_strength || 50
    const payload = {
      model: modelConfig.model,
      format: 'jpeg',
      prompt: params.prompt,
      hide_watermark: true,
      return_binary: false,
      lora_strength: loraStrength,
      negative_prompt: negativePrompt,
      // seed: 0,
      height: params.height || 1024,
      width: params.width || 1024,
      ...modelConfig.params,
      ...(params.style && params.style !== 'None' ? { style_preset: params.style } : {})
    }

    const options = {
      method: 'POST',
      headers: { Authorization: `Bearer ${config.venice.apiKey}`, 'Content-Type': 'application/json' },
      signal: AbortSignal.timeout(90000),
      body: JSON.stringify(payload)
    }

    const response = await fetch(`${veniceApiUrl}/image/generate`, options).then((response) => response.json())
    // console.log('Venice API response:', response)

    return response
  },

  enhancePrompt: async (params: any, event: any) => {
    // Get runtime config for Venice API
    const config = useRuntimeConfig(event)

    if (!config.venice?.apiKey) {
      throw new Error('Venice API key not defined')
    }

    const model = textModels.small.name
    const prompt = generatePromptForEnhancement(params.prompt, params.style)
    const payload = {
      model: model,
      venice_parameters: {
        include_venice_system_prompt: true,
        strip_thinking_response: true,
        disable_thinking: true,
        enable_web_search: 'off'
      },
      messages: [
        { role: 'system', content: enhanceInstructions },
        { role: 'user', content: prompt }
      ],
      response_format: {
        type: 'json_schema',
        json_schema: {
          name: 'prompt_suggestion_response',
          strict: true,
          schema: {
            type: 'object',
            properties: {
              prompt: {
                type: 'string'
              }
            },
            required: ['prompt'],
            additionalProperties: false
          }
        }
      }
    }

    const options = {
      method: 'POST',
      headers: { Authorization: `Bearer ${config.venice.apiKey}`, 'Content-Type': 'application/json' },
      signal: AbortSignal.timeout(25000),
      body: JSON.stringify(payload)
    }

    const response = await fetch(`${veniceApiUrl}/chat/completions`, options).then((response) => response.json())
    // console.log('Venice API response:', response)

    return response
  },

  getAvailableTokens: async function (event: any) {
    try {
      const config = useRuntimeConfig(event)

      if (!config.venice?.apiKey) {
        console.warn('Venice API key not found in environment variables')
        return { DIEM: 0 }
      }

      const response = await fetch(`${veniceApiUrl}/api_keys/rate_limits`, {
        headers: {
          Authorization: `Bearer ${config.venice.apiKey}`
        }
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch available tokens: ${response.status} ${response.statusText}`)
      }

      const result = await response.json()
      return result.data?.balances || { DIEM: 0 }
    } catch (error) {
      console.error('Failed to fetch available Venice.ai tokens:', error)
      return { DIEM: 0 }
    }
  }
}

export default helper
