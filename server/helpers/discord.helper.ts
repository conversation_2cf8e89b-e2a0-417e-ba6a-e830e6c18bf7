export enum DiscordColor {
  blue = 3900150,
  amber = 16096779,
  teal = 1357990,
  rose = 16007006,
  violet = 9133302
}

const helper = {
  webhook: async (event: any, payload: { title: string; message: string; color?: DiscordColor; embeds?: { name: string; value: string }[] }) => {
    // Get runtime config for Venice API
    const config = useRuntimeConfig(event)

    if (!config.discord?.webhookUrl) {
      console.log('Webhook log: ' + JSON.stringify({ title: payload.title, message: payload.message }))
      return
    }

    const webhookUrl = config.discord.webhookUrl

    try {
      const content = {
        title: payload.title,
        description: payload.message,
        timestamp: new Date().toISOString(),
        ...(payload.color ? { color: payload.color } : {})
      }

      const body = {
        embeds: payload.embeds ? [{ ...content, fields: payload.embeds.map((e) => ({ ...e, inline: true })) }] : [content]
      }

      const options = {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body)
      }

      await fetch(webhookUrl, options)

      console.log(`Discord message sent: "${payload.title}: ${payload.message}"`)
      return true
    } catch (error) {
      console.error('Error sending webhook message:', error)
      return false
    }
  }
}

export default helper
