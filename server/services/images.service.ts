import { serverSupabaseClient, serverSupabaseServiceRole } from '#supabase/server'
import { User } from '@supabase/supabase-js'
import { randomUUID } from 'crypto'
import PQueue from 'p-queue'
import pThrottle from 'p-throttle'
import { models } from '~/config/venice.config'
import cloudflareHelper from '~/server/helpers/cloudflare.helper'
import discordHelper, { DiscordColor } from '~/server/helpers/discord.helper'
import imagesHelper from '~/server/helpers/images.helper'
import veniceHelper from '~/server/helpers/venice.helper'
import { Database } from '~/types/database.types'

const service = {
  queues: {} as any,
  hashQueue: {} as any,

  init: () => {
    const modeNames = new Set([...Object.values(models).map((m) => m.model)])
    for (let model of modeNames) {
      service.queues[model] = pThrottle({
        limit: 20,
        interval: 62000
      })
    }

    service.hashQueue = new PQueue({ concurrency: 1 })
  },

  generate: async (event: any, user: User, params: any) => {
    console.log('Generate new images by user:', user.email || user.id)

    try {
      const client = await serverSupabaseClient<Database>(event)
      const serviceClient = await serverSupabaseServiceRole<Database>(event)

      let imageNumber = params.images || 1
      imageNumber = Math.min(Math.max(imageNumber, 1), 4)

      const modelConfig = models[params.model]

      const imageCredits = modelConfig.credits || 1
      const totalCredits = imageCredits * imageNumber

      // check database remaining credit for this user
      const { data: profile } = await serviceClient.from('profiles').select().eq('id', user.id).limit(1).single()
      if (!profile || !profile.daily_credits || profile.daily_credits < totalCredits) {
        throw new Error('Not enough credits')
      }

      // decrement available credits for this user
      const { data, error: dbRpcError } = await serviceClient.rpc('decrement_profile_credits', { x: totalCredits, user_id: user.id })
      if (dbRpcError) {
        throw new Error(`Failed to decrement credits: ${dbRpcError.message}`)
      }

      // store prompt
      const promptId = randomUUID()
      const { error: dbPromptInsertError } = await client.from('prompts').insert({
        id: promptId,
        user_id: user.id,
        model: params.model,
        text: params.prompt,
        images: imageNumber,
        ...(params.style && params.style !== 'None' ? { style: params.style } : {})
      })
      if (dbPromptInsertError) {
        console.error(dbPromptInsertError)
        throw new Error(`Failed to insert image: ${dbPromptInsertError.message}`)
      }

      for (let i = 0; i < imageNumber; i++) {
        const imageId = randomUUID()

        const { error: dbImageInsertError } = await client.from('images').insert({
          id: imageId,
          user_id: user.id,
          location: '-',
          status: 'pending',
          prompt_id: promptId,
          model: params.model,
          credit_cost: imageCredits,
          ...(params.style && params.style !== 'None' ? { style: params.style } : {}),
          ...(params.folder ? { folder_id: params.folder } : {})
        })
        if (dbImageInsertError) {
          console.error(dbImageInsertError)
          throw new Error(`Failed to insert image: ${dbImageInsertError.message}`)
        }

        // generate image
        const throttle = service.queues[modelConfig.model]
        const generateImageThrottled = throttle(async () => await veniceHelper.generateImage(params, event))

        generateImageThrottled()
          .then(async (imageResponse: any) => {
            // console.log('Image response received: ' + JSON.stringify(imageResponse))
            console.log('Image response received')

            if (imageResponse.error) {
              throw new Error(`Error generating image: ${imageResponse.error}`)
            }
            if (imageResponse.issues?.length) {
              throw new Error(`Error generating image: ${imageResponse.issues[0].message}`)
            }
            if (!imageResponse.images?.length) {
              throw new Error('Error generating image: No images returned')
            }

            // convert base64 image data to binary buffer
            const base64Data = imageResponse.images[0].replace(/^data:image\/\w+;base64,/, '')
            const imageBuffer = Buffer.from(base64Data, 'base64')

            // upload to storage
            console.log('Starting R2 upload')
            const { url } = await cloudflareHelper.upload(user.id, imageId, imageBuffer, event)

            // update database
            console.log('Starting DB update')
            const { error: dbImageUpdateError } = await client
              .from('images')
              .update({ location: url, status: 'completed' })
              .eq('id', imageId)
              .eq('user_id', user.id)
            if (dbImageUpdateError) {
              console.error(dbImageUpdateError)
              throw new Error(`Failed to update image: ${dbImageUpdateError.message}`)
            }

            // async compute thumbhash
            service.hashQueue.add(async () => {
              console.log('Starting thumbhash computation')
              const thumbhash = await imagesHelper.computeThumbhash(imageBuffer)

              console.log('Starting thumbhash DB update')
              const { error: dbHashUpdateError } = await client.from('images').update({ thumbhash: thumbhash }).eq('id', imageId).eq('user_id', user.id)
              if (dbHashUpdateError) {
                console.error(dbHashUpdateError)
              }
            })

            console.log('Image generated successfully')

            discordHelper.webhook(event, {
              title: `Image generated`,
              message: `Image generated successfully for user ${user.id} ${user.is_anonymous ? '(anonymous)' : ''}`,
              color: DiscordColor.teal,
              embeds: [{ name: 'Model', value: params.model }]
            })
          })
          .catch(async (error: any) => {
            // TODO: review and re-enable retry logic
            /*
            if (error instanceof RetryError) {
              throw error
            }
            */

            console.error(`Error generating image: ${error.message}`)

            // update image status on database
            await client.from('images').update({ status: 'error' }).eq('id', imageId).eq('user_id', user.id)

            // restore user credits
            const { data, error: dbRpcError } = await serviceClient.rpc('increment_profile_credits', { x: imageCredits, user_id: user.id })
            if (dbRpcError) {
              throw new Error(`Failed to increment credits: ${error.message}`)
            }

            discordHelper.webhook(event, {
              title: `Image Error`,
              message: `Error generating image for user ${user.id}: ${error.message}`,
              color: DiscordColor.rose,
              embeds: [{ name: 'Model', value: params.model }]
            })
          })
      }

      return { success: true }
    } catch (error) {
      console.error(`Error starting image generation`, error)
      throw error
    }
  },

  delete: async (event: any, user: User, imageIds: string[]) => {
    const client = await serverSupabaseClient<Database>(event)

    const { count, error } = await client.from('images').select('*', { count: 'exact', head: true }).in('id', imageIds).eq('user_id', user.id)
    if (!count || count !== imageIds.length) {
      throw createError({ statusCode: 400, message: 'Bad request' })
    }

    const locations = imageIds.map((id: any) => `${user.id}/${id}.webp`)
    await cloudflareHelper.delete(locations, event)

    const deleteResult = await client.from('images').delete().in('id', imageIds).eq('user_id', user.id)
    if (deleteResult.error) {
      throw createError({ statusCode: 500, message: 'Failed to delete image' })
    }

    return { success: true }
  }
}

// init rate limiting queues
service.init()

export default service
