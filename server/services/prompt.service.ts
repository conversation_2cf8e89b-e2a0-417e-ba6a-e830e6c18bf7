import { User } from '@supabase/supabase-js'
import veniceHelper from '~/server/helpers/venice.helper'

const service = {
  enhance: async (event: any, user: User, params: any) => {
    console.log('Enhance prompt for user:', user.email || user.id)

    try {
      const result = await veniceHelper.enhancePrompt(params, event)

      const rawMessage = result.choices.length && result.choices[0].message?.content
      if (!rawMessage) {
        throw new Error('No message returned')
      }

      const prompt = JSON.parse(rawMessage)?.prompt
      if (!prompt) {
        throw new Error('No prompt returned')
      }

      return { success: true, prompt: prompt }
    } catch (error) {
      console.error(`Error starting image generation`, error)
      return { success: false }
    }
  }
}

export default service
