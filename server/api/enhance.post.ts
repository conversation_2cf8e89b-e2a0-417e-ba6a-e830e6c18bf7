import { serverSupabaseUser } from '#supabase/server'
import { enhanceStyles } from '~/config/venice.config'
import promptService from '~/server/services/prompt.service'

export default defineEventHandler(async (event) => {
  const user = await serverSupabaseUser(event)
  if (!user) {
    throw createError({ statusCode: 401, message: 'Unauthorized' })
  }

  const body = await readBody(event)

  // validate params
  if (!body.prompt) {
    throw createError({ statusCode: 400, message: 'Bad request' })
  }
  if (body.style && !(body.style in enhanceStyles)) {
    throw createError({ statusCode: 400, message: 'Bad request' })
  }

  return promptService.enhance(event, user, body)
})
