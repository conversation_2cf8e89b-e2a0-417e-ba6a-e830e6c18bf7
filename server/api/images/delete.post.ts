import { serverSupabaseUser } from '#supabase/server'
import imagesService from '~/server/services/images.service'

export default defineEventHandler(async (event) => {
  const user = await serverSupabaseUser(event)
  if (!user) {
    throw createError({ statusCode: 401, message: 'Unauthorized' })
  }

  const body = await readBody(event)
  if (!body.images) {
    throw createError({ statusCode: 400, message: 'Bad request' })
  }

  return imagesService.delete(event, user, body.images)
})
