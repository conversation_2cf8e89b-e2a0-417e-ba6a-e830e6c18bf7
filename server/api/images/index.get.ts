import { serverSupabaseServiceRole } from '#supabase/server'
import { Database } from '~/types/database.types'

// handle GET requests for the `api/images` endpoint
export default defineEventHandler(async (event) => {
  const serviceClient = await serverSupabaseServiceRole<Database>(event)
  return serviceClient.from('images').select().eq('status', 'completed').eq('is_public', true).order('created_at', { ascending: true }).limit(20)
})
