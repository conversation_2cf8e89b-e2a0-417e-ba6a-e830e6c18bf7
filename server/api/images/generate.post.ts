import { serverSupabaseUser } from '#supabase/server'
import { models } from '~/config/venice.config'
import imagesService from '~/server/services/images.service'

// handle POST requests for the `api/images` endpoint
export default defineEventHandler(async (event) => {
  const user = await serverSupabaseUser(event)
  if (!user) {
    throw createError({ statusCode: 401, message: 'Unauthorized' })
  }

  const body = await readBody(event)

  // validate params
  if (!(body.model in models)) {
    throw createError({ statusCode: 400, message: 'Bad request' })
  }

  return imagesService.generate(event, user, body)
})
