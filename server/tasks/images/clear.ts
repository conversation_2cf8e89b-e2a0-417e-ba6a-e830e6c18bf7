import { createClient } from '@supabase/supabase-js'

export default defineTask({
  meta: {
    name: 'images:clear',
    description: 'Clear old pending images from database'
  },
  run: async ({ payload, context }) => {
    const supabaseUrl = process.env.SUPABASE_URL
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Supabase URL or service key not defined')
    }

    const client = createClient(supabaseUrl, supabaseServiceKey)

    // delete 'pending' images not completed within 10 minutes
    const tenMinutesAgo = new Date(Date.now() - 1000 * 60 * 10).toISOString()
    await client.from('images').delete().eq('status', 'pending').lt('created_at', tenMinutesAgo)

    // delete 'error' images not completed within 10 minutes
    const oneDayAgo = new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString()
    await client.from('images').delete().eq('status', 'error').lt('created_at', oneDayAgo)

    return { result: 'Success' }
  }
})
