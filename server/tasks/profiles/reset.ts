import { createClient } from '@supabase/supabase-js'
import subscriptionsConfig from '~/config/subscriptions.config'

export default defineTask({
  meta: {
    name: 'profiles:reset',
    description: 'Reset profiles daily credits'
  },
  run: async ({ payload, context }) => {
    const supabaseUrl = process.env.SUPABASE_URL
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Supabase URL or service key not defined')
    }

    const client = createClient(supabaseUrl, supabaseServiceKey)

    const oneDayAgo = new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString()
    for (const subscription of Object.keys(subscriptionsConfig)) {
      const dailyCredits = subscriptionsConfig[subscription as keyof typeof subscriptionsConfig].credits
      if (!dailyCredits) {
        continue
      }

      await client
        .from('profiles')
        .update({ daily_credits: dailyCredits, reset_at: new Date().toISOString() })
        .eq('subscription', subscription)
        .lt('daily_credits', dailyCredits)
        .lt('reset_at', oneDayAgo)
    }

    return { result: 'Success' }
  }
})
