<template>
  <div class="dark w-full">
    <NuxtPwaManifest />
    <NuxtRouteAnnouncer />

    <NuxtLayout>
      <NuxtPage />
      <!--<NuxtPage :page-key="(route) => route.fullPath" />-->
    </NuxtLayout>

    <ClientOnly>
      <Toaster theme="dark" richColors class="pointer-events-auto" />
      <!--<Toaster theme="dark" richColors closeButton class="pointer-events-auto z-100" @click.stop.prevent />-->
    </ClientOnly>
  </div>
</template>

<script setup lang="ts">
import { Toaster } from '@/components/ui/sonner'
import 'vue-sonner/style.css'

const user: any = useSupabaseUser()
const accountStore = useAccount()
accountStore.init()

watch(user, () => accountStore.refresh(), { immediate: true })

const route = useRoute()
onMounted(async () => {
  if (route.query?.error && route.query?.error_code === 'identity_already_exists') {
    await nextTick()
    $toast.error('This user already exists. Please sign in to that account.')
  }

  // check PWA status
  const pwa = usePWA()
  console.log('PWA installed:', pwa?.isPWAInstalled)
})
</script>
