import { useLocalStorage } from '@vueuse/core'
import { defineStore } from 'pinia'

const defaultSettings = {
  view: 'grid'
}
const defaultModel = {
  name: 'venice-sd35'
}

export const useSettings = defineStore('settings', {
  state: () => ({
    settings: useLocalStorage('settings', { ...defaultSettings }),
    model: useLocalStorage('model', { ...defaultModel }),
    mode: useLocalStorage('mode', 'create'),
    devMode: import.meta.dev ? true : false
  }),
  actions: {}
})
