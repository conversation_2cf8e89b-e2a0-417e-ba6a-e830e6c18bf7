import { useLocalStorage } from '@vueuse/core'
import { defineStore } from 'pinia'

export const useFolders = defineStore('folders', {
  state: () => ({
    folders: useLocalStorage('folders', [] as any[])
  }),
  actions: {
    async refresh() {
      const user = useSupabaseUser()
      const client = useSupabaseClient()

      if (!user.value) return

      const { data } = await client.from('folders').select().eq('user_id', user.value.id)
      this.folders = data || []
      return this.folders
    },
    async create(name: string, color?: string) {
      const user = useSupabaseUser()
      const client = useSupabaseClient()

      if (!user.value) return

      await client.from('folders').insert({ user_id: user.value.id, name: name, color: color })

      await this.refresh()
    },
    async delete(folderId: string) {
      const user = useSupabaseUser()
      const client = useSupabaseClient()

      if (!user.value) return

      await client.from('folders').delete().eq('id', folderId).eq('user_id', user.value.id)

      await this.refresh()
    }
  }
})
