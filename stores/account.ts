import { defineStore } from 'pinia'

const defaultAccount = {
  daily_credits: 0,
  additional_credits: 0,
  subscription: 'free',
  reset_at: null,
  avatar: '',
  name: ''
}
export const useAccount = defineStore('account', {
  state: () => ({
    account: { ...defaultAccount }
  }),
  actions: {
    async refresh(newAccountData?: any) {
      const user = useSupabaseUser()
      const supabase = useSupabaseClient()

      if (!user.value) {
        this.account = { ...defaultAccount }
        return
      }

      if (newAccountData) {
        this.account = { ...newAccountData }
        return
      }

      const { data } = await supabase
        .from('profiles')
        .select('daily_credits, additional_credits, reset_at, subscription')
        .eq('id', user.value.id)
        .limit(1)
        .single()
      this.account = { ...this.account, ...data }
    },
    async init() {
      const supabase = useSupabaseClient()
      const realtimeChannel = supabase
        .channel('public:profiles')
        .on('postgres_changes', { event: 'UPDATE', schema: 'public', table: 'profiles' }, (payload) => {
          if (payload.eventType === 'UPDATE') {
            this.refresh(payload.new)
          }
        })
      realtimeChannel.subscribe()
    }
  }
})
