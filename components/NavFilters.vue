<script setup lang="ts">
import { LayoutGrid, LayoutDashboard, Grid3x3 } from 'lucide-vue-next'

const settingsStore = useSettings()
</script>

<template>
  <SidebarGroup class="group-data-[collapsible=icon]:hidden">
    <SidebarGroupLabel class="relative justify-between items-center"> Filters & Views </SidebarGroupLabel>
    <SidebarMenu>
      <SidebarMenuItem>
        <ToggleGroup type="single" v-model="settingsStore.settings.view" class="w-full border flex justify-evenly">
          <ToggleGroupItem value="grid" aria-label="Toggle bold">
            <LayoutGrid class="h-4 w-4" />
          </ToggleGroupItem>
          <ToggleGroupItem value="masonry" aria-label="Toggle italic">
            <LayoutDashboard class="h-4 w-4" />
          </ToggleGroupItem>
          <ToggleGroupItem value="compact" aria-label="Toggle underline">
            <Grid3x3 class="h-4 w-4" />
          </ToggleGroupItem>
        </ToggleGroup>
      </SidebarMenuItem>
    </SidebarMenu>
  </SidebarGroup>
</template>
