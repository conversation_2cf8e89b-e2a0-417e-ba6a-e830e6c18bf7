<script setup lang="ts">
import { CircleFadingArrowUp, Power, Menu } from 'lucide-vue-next'

const user = useSupabaseUser()
</script>

<template>
  <div class="flex justify-between items-center py-2 pl-4 lg:pl-0">
    <AppLogo></AppLogo>

    <div class="flex gap-1">
      <Button v-if="user" as-child variant="ghost" size="lg" class="px-0 lg:px-4">
        <NuxtLink to="/images" class="font-display">Open App</NuxtLink>
      </Button>
      <Button v-else as-child variant="ghost" size="lg">
        <NuxtLink to="/login" class="font-display">Login</NuxtLink>
      </Button>

      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <Button variant="ghost" size="lg">
            <Menu class="size-6"></Menu>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" side="top" class="w-56">
          <DropdownMenuItem>
            <NuxtLink to="/upgrade" class="grow font-display">Pricing</NuxtLink>
            <CircleFadingArrowUp class="ml-2 h-4 w-4" />
          </DropdownMenuItem>
          <template v-if="user">
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <NuxtLink to="/logout" class="grow font-display">Logout</NuxtLink>
              <Power class="ml-2 h-4 w-4" />
            </DropdownMenuItem>
          </template>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  </div>
</template>
