<template>
  <NuxtLink to="/" class="flex items-center justify-center gap-2.5">
    <div class="aspect-square size-8">
      <NuxtImg src="/images/logo_small.png" alt="Logo" class="flex size-8 items-center justify-center rounded-md" />
    </div>
    <div v-if="!compact" class="text-lg">
      <span class="font-medium font-display tracking-wide truncate">VeniceShelf</span>
    </div>
  </NuxtLink>
</template>

<script lang="ts" setup>
const props = defineProps<{
  compact?: boolean
}>()
</script>
