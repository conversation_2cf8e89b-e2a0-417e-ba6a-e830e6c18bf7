<template>
  <div ref="infiniteLoading" class="w-full flex justify-center">
    <slot v-if="state == 'loading'">
      <AppLoader class="my-5" />
    </slot>
    <slot v-if="state == 'complete'" name="complete"></slot>
    <slot v-if="state == 'error'" name="error"></slot>
  </div>
</template>

<style scoped>
  .state-error {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .retry {
    margin-top: 8px;
    padding: 2px 6px 4px 6px;
    width: 60px;
    color: inherit;
    font-size: 14px;
    font-family: inherit;
    background: transparent;
    border: 2px solid currentColor;
    border-radius: 5px;
    outline: none;
    cursor: pointer;
  }
  .retry:hover {
    opacity: 0.8;
  }
</style>

<script setup lang="ts">
  const emit = defineEmits(['infinite'])

  const props = defineProps({
    top: { type: Boolean, required: false },
    target: { type: [String, Boolean], required: false },
    distance: { type: Number, required: false, default: 50 },
    identifier: { required: false },
    firstLoad: { type: Boolean, required: false, default: true },
    slots: { type: Object, required: false }
  })

  const stateHandler = (state: Ref<string>) => ({
    loading() {
      state.value = 'loading'
    },
    loaded() {
      state.value = 'loaded'
    },
    complete() {
      state.value = 'complete'
    },
    error() {
      state.value = 'error'
    }
  })

  const initEmitter = (emit, stateHandler, params) => {
    return () => {
      const parentEl = params.parentEl || document.documentElement
      params.prevHeight = parentEl.scrollHeight
      stateHandler.loading()
      emit('infinite', stateHandler)
    }
  }

  const isVisible = (el, view) => {
    const elRect = el.getBoundingClientRect()
    if (!view) return elRect.top >= 0 && elRect.bottom <= window.innerHeight
    const viewRect = view.getBoundingClientRect()
    return elRect.top >= viewRect.top && elRect.bottom <= viewRect.bottom
  }

  const startObserver = params => {
    params.parentEl = document.querySelector(params.target) || null
    let rootMargin = `0px 0px ${params.distance}px 0px`
    if (params.top) rootMargin = `${params.distance}px 0px 0px 0px`
    const observer = new IntersectionObserver(
      entries => {
        const entry = entries[0]
        if (entry.isIntersecting) {
          if (params.firstLoad) params.emit()
          params.firstLoad = true
        }
      },
      { root: params.parentEl, rootMargin }
    )
    observer.observe(params.infiniteLoading.value)
    return observer
  }

  let observer: any = null
  const infiniteLoading = ref(null)
  const state = ref('ready')
  const { top, firstLoad, target, distance } = props
  const { identifier } = toRefs(props)

  const params = {
    infiniteLoading,
    target,
    top,
    firstLoad,
    distance,
    prevHeight: 0,
    parentEl: null
  }
  params.emit = initEmitter(emit, stateHandler(state), params)

  const stateWatcher = () =>
    watch(state, async newVal => {
      const parentEl = params.parentEl || document.documentElement
      await nextTick()
      if (newVal == 'loaded' && top) parentEl.scrollTop = parentEl.scrollHeight - params.prevHeight
      if (newVal == 'loaded' && isVisible(infiniteLoading.value, params.parentEl)) params.emit()
      if (newVal == 'complete') observer.disconnect()
    })

  const identifierWatcher = () =>
    watch(identifier, () => {
      state.value = 'ready'
      observer.disconnect()
      observer = startObserver(params)
    })

  onMounted(() => {
    observer = startObserver(params)
    stateWatcher()
    if (identifier) identifierWatcher()
  })

  onUnmounted(() => {
    observer.disconnect()
  })
</script>
