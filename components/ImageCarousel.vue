<script setup lang="ts">
import { useClipboard, watchOnce } from '@vueuse/core'
import { type CarouselApi } from '@/components/ui/carousel'

const config = useRuntimeConfig()
const supabase = useSupabaseClient()

const emit = defineEmits(['regenerate', 'deleted'])

const props = withDefaults(
  defineProps<{
    images: any[]
    index: number
  }>(),
  {
    images: () => [],
    index: 0
  }
)

const emblaMainApi = ref<CarouselApi>()
const emblaThumbnailApi = ref<CarouselApi>()

const selectedIndex = ref(props.index)
const selectedImage = computed(() => props.images[selectedIndex.value])

function onSelect() {
  if (!emblaMainApi.value || !emblaThumbnailApi.value) return
  selectedIndex.value = emblaMainApi.value.selectedScrollSnap()
  emblaThumbnailApi.value.scrollTo(emblaMainApi.value.selectedScrollSnap())
}

function onThumbClick(index: number) {
  if (!emblaMainApi.value || !emblaThumbnailApi.value) return
  emblaMainApi.value.scrollTo(index)
}

watchOnce(emblaMainApi, (emblaMainApi) => {
  if (!emblaMainApi) return

  onThumbClick(props.index)
  emblaMainApi.on('select', onSelect)
  emblaMainApi.on('reInit', onSelect)
})

const { copy, copied, isSupported } = useClipboard()
watch(copied, (value) => {
  if (value) {
    $toast.success('Prompt successfully copied to clipboard')
  }
})

const isUpdatingImages = ref(false)

const regenerateImage = async () => {
  if (!selectedImage.value) return

  if (isUpdatingImages.value) return

  try {
    isUpdatingImages.value = true

    // get image aspect ratio
    const imageUrl = `${config.public.imageBaseUrl}/${selectedImage.value.location}`
    const webpBlob = await fetch(imageUrl, { cache: 'no-store' }).then((response) => response.blob())

    // draw in canvass and convert to png
    const imageElement: HTMLImageElement = await blobToImage(webpBlob)
    const width = imageElement.naturalWidth
    const height = imageElement.naturalHeight

    emit('regenerate', {
      prompt: prompts.value[selectedImage.value.prompt_id]?.text,
      model: selectedImage.value.model,
      style: selectedImage.value.style,
      width: width,
      height: height,
      folderId: selectedImage.value.folder_id
    })
  } catch (error) {
    $toast.error('Error regenerating image, please try again later.')
  } finally {
    isUpdatingImages.value = false
  }
}

const togglePublic = async () => {
  if (!selectedImage.value) return

  if (isUpdatingImages.value) return

  try {
    isUpdatingImages.value = true

    const isPublic = selectedImage.value.is_public
    await supabase.from('images').update({ is_public: !isPublic }).eq('id', selectedImage.value.id)

    selectedImage.value.is_public = !isPublic

    $toast.success(isPublic ? 'This image is now private' : 'This image is now public')
  } catch (error) {
    $toast.error('Error deleting image, please try again later.')
  } finally {
    isUpdatingImages.value = false
  }
}

const blobToImage = (blob: Blob): Promise<HTMLImageElement> => {
  return new Promise((resolve) => {
    const url = URL.createObjectURL(blob)
    let img = new Image()
    img.onload = () => {
      URL.revokeObjectURL(url)
      resolve(img)
    }
    img.src = url
  })
}

const copyToClipboard = async () => {
  if (!selectedImage.value) return

  if (isUpdatingImages.value) return

  if (!navigator.clipboard?.write) {
    $toast.error('Copying images is not supported in your browser.')
    return
  }

  try {
    isUpdatingImages.value = true

    // get image
    const imageUrl = `${config.public.imageBaseUrl}/${selectedImage.value.location}`
    const webpBlob = await fetch(imageUrl, { cache: 'no-store' }).then((response) => response.blob())

    // draw in canvass and convert to png
    const imageElement: HTMLImageElement = await blobToImage(webpBlob)
    const canvas = document.createElement('canvas')
    canvas.width = imageElement.naturalWidth
    canvas.height = imageElement.naturalHeight
    canvas.setAttribute('crossOrigin', 'anonymous')
    const ctx = canvas.getContext('2d')
    if (!ctx) {
      throw new Error('Failed to get canvas context')
    }

    ctx.drawImage(imageElement, 0, 0)
    const blob: Blob = await new Promise((resolve) => canvas.toBlob(resolve, 'image/png'))

    // copy to clipboard
    await navigator.clipboard.write([
      new ClipboardItem({
        'image/png': blob
      })
    ])

    $toast.success('Image copied to clipboard')
  } catch (error) {
    console.error('Error copying image to clipboard:', error)
    $toast.error('Error copying image to clipboard')
  } finally {
    isUpdatingImages.value = false
  }
}

const downloadImage = async () => {
  if (!selectedImage.value) return

  if (isUpdatingImages.value) return

  try {
    isUpdatingImages.value = true

    const imageUrl = `${config.public.imageBaseUrl}/${selectedImage.value.location}`
    const webpBlob = await fetch(imageUrl, { cache: 'no-store' }).then((response) => response.blob())

    const imageElement: HTMLImageElement = await blobToImage(webpBlob)
    const canvas = document.createElement('canvas')
    canvas.width = imageElement.naturalWidth
    canvas.height = imageElement.naturalHeight
    canvas.setAttribute('crossOrigin', 'anonymous')
    const ctx = canvas.getContext('2d')
    if (!ctx) {
      throw new Error('Failed to get canvas context')
    }

    ctx.drawImage(imageElement, 0, 0)
    const blob: Blob = await new Promise((resolve) => canvas.toBlob(resolve, 'image/jpeg'))

    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `veniceshelf-${selectedImage.value.id}.jpg`
    link.click()
    URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Error downloading image:', error)
    $toast.error('Error downloading image')
  } finally {
    isUpdatingImages.value = false
  }
}

const deleteImage = async () => {
  if (isUpdatingImages.value) return

  try {
    isUpdatingImages.value = true

    await $fetch(`/api/images/delete`, {
      method: 'post',
      body: {
        images: [selectedImage.value.id]
      }
    })

    emit('deleted', selectedImage.value.id)
    $toast.success('Image deleted successfully')
  } catch (error) {
    $toast.error('Error deleting image, please try again later.')
  } finally {
    isUpdatingImages.value = false
  }
}

const prompts = ref({} as any)
const isLoadingPrompts = ref(false)
onMounted(() => {
  // load prompts
  isLoadingPrompts.value = true

  try {
    const promptIds = [...new Set(props.images.map((i) => i.prompt_id))]
    supabase
      .from('prompts')
      .select()
      .in('id', promptIds)
      .then((result) => {
        const records = result.data || []
        prompts.value = records.reduce((acc, prompt) => {
          acc[prompt.id] = prompt
          return acc
        }, {} as any)
      })
  } catch (ignore) {
  } finally {
    isLoadingPrompts.value = false
  }
})
</script>

<template>
  <div>
    <Carousel class="w-full" @init-api="(val) => (emblaMainApi = val)">
      <CarouselContent class="grow">
        <CarouselItem v-for="(image, index) in images" :key="index" class="">
          <div class="h-full max-h-[calc(100dvh-320px)] w-full flex items-center justify-center">
            <UnLazyImage
              :thumbhash="image.thumbhash"
              :src="`${config.public.imageBaseUrl}/${image.location}`"
              :autoSizes="true"
              class="h-full w-auto object-contain rounded"
            />
          </div>
        </CarouselItem>
      </CarouselContent>
      <CarouselPrevious class="hidden lg:inline-flex" />
      <CarouselNext class="hidden lg:inline-flex" />
    </Carousel>

    <div v-if="selectedImage" class="mx-auto flex items-center justify-center gap-2 my-1">
      <Badge variant="secondary" class="px-5">
        Model: <span class="uppercase font-semibold">{{ selectedImage.model.split('@')[0] }}</span></Badge
      >
      <Badge variant="secondary" class="px-5">
        Quality:
        <span v-if="selectedImage.model.split('@').length > 1" class="uppercase font-semibold">{{ selectedImage.model.split('@')[1] }}</span>
        <span v-else class="uppercase font-semibold">standard</span></Badge
      >
      <Badge variant="secondary" class="px-5">
        Style:
        <span class="uppercase font-semibold">{{ selectedImage.style || 'none' }}</span></Badge
      >
    </div>

    <div class="w-full mx-auto flex flex-col lg:flex-row items-center justify-between gap-2 mt-1 py-1 lg:px-4 rounded-md">
      <div class="max-w-5xl order-2 lg:order-1 flex items-center gap-2">
        <span class="italic text-xs line-clamp-3">{{
          isLoadingPrompts ? '...' : (selectedImage && prompts[selectedImage.prompt_id]?.text) || 'Prompt not available'
        }}</span>
        <Button
          v-if="isSupported"
          variant="ghost"
          size="sm"
          :disabled="isLoadingPrompts || !selectedImage || !prompts[selectedImage.prompt_id]?.text"
          @click="copy(prompts[selectedImage.prompt_id]?.text)"
        >
          <Icon name="lucide:clipboard" class="text-muted-foreground h-4 w-4" />
        </Button>
      </div>
      <div
        class="bg-card text-muted-foreground relative z-10 w-full flex justify-evenly order-1 lg:order-2 lg:w-auto lg:grid lg:grid-cols-3 items-center gap-2 border border-input rounded-md"
      >
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger
              ><Button variant="ghost" size="sm" @click="regenerateImage()" class="text-base"> <Icon name="lucide:rotate-ccw" /> </Button
            ></TooltipTrigger>
            <TooltipContent :sideOffset="0">
              <p>Regenerate image</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger
              ><Button variant="ghost" size="sm" @click="() => {}" class="text-base" :disabled="true"> <Icon name="lucide:image-upscale" /> </Button
            ></TooltipTrigger>
            <TooltipContent :sideOffset="0">
              <p>Upscale image (coming soon)</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger
              ><Button variant="ghost" size="sm" @click="togglePublic()" class="text-base" :disabled="isUpdatingImages">
                <Icon :name="selectedImage.is_public ? 'lucide:eye-off' : 'lucide:eye'" /> </Button
            ></TooltipTrigger>
            <TooltipContent :sideOffset="0">
              <p>{{ selectedImage.is_public ? 'Make private' : 'Make public' }}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger
              ><Button variant="ghost" size="sm" @click="copyToClipboard()" class="text-base" :disabled="isUpdatingImages"> <Icon name="lucide:copy" /> </Button
            ></TooltipTrigger>
            <TooltipContent :sideOffset="0">
              <p>Copy to clipboard</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger
              ><Button variant="ghost" size="sm" @click="downloadImage()" class="text-base" :disabled="isUpdatingImages">
                <Icon name="lucide:download" /> </Button
            ></TooltipTrigger>
            <TooltipContent :sideOffset="0">
              <p>Download image</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger
              ><Button variant="ghost" size="sm" @click="deleteImage()" class="text-base" :disabled="isUpdatingImages">
                <Icon name="lucide:trash" class="text-destructive-foreground h-4 w-4" /> </Button
            ></TooltipTrigger>
            <TooltipContent :sideOffset="0">
              <p>Delete Image</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </div>

    <Carousel class="relative w-full max-w-md mx-auto mt-5" @init-api="(val) => (emblaThumbnailApi = val)">
      <CarouselContent class="ml-0">
        <CarouselItem v-for="(image, index) in images" :key="`thumb-${index}`" class="pl-0 basis-1/4 lg:basis-1/5 cursor-pointer" @click="onThumbClick(index)">
          <div class="p-1" :class="index === selectedIndex ? '' : 'opacity-50'">
            <UnLazyImage
              :thumbhash="image.thumbhash"
              :src="`${config.public.imageBaseUrl}/${image.location}`"
              class="size-18 lg:size-20 aspect-square object-cover rounded"
            />
          </div>
        </CarouselItem>
      </CarouselContent>
    </Carousel>
  </div>
</template>
