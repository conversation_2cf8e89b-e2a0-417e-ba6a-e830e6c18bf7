<script setup lang="ts">
import { type LucideIcon } from 'lucide-vue-next'

defineProps<{
  items: {
    title: string
    action: Function
    icon: LucideIcon
    isActive?: boolean
    isDisabled?: boolean
  }[]
}>()

const settingsStore = useSettings()
</script>

<template>
  <SidebarGroup>
    <SidebarMenu>
      <SidebarMenuItem v-for="item in items" :key="item.title" as-child>
        <SidebarMenuButton as-child :tooltip="item.title" :is-active="item.isActive">
          <a @click="item.action()" class="">
            <component :is="item.icon" />
            <span class="font-display grow">{{ item.title }}</span>

            <Badge v-if="item.isDisabled" variant="outline" class="text-2xs border-destructive/50 py-0">Soon</Badge>
          </a>
        </SidebarMenuButton>
      </SidebarMenuItem>
    </SidebarMenu>
  </SidebarGroup>
</template>
