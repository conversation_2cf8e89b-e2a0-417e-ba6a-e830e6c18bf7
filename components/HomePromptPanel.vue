<script setup lang="ts">
import { Loader2 } from 'lucide-vue-next'
import { defaultModel, maxPromptLength } from '~/config/venice.config'

const supabase = useSupabaseClient()
const user = useSupabaseUser()

const prompt = ref('')
const modelName = defaultModel.name
const { width, height } = defaultModel.aspectRatio[0]
const imageNumber = 4
const isGenerating = ref(false)

const generateImage = async () => {
  if (isGenerating.value) return

  if (prompt.value.length < 15) {
    $toast.error(`Prompt is too short. Try to describe more details of the image you want to generate.`)
    return
  }
  if (prompt.value.length > maxPromptLength) {
    $toast.error(`Prompt is too long. Please keep it under ${maxPromptLength} characters.`)
    return
  }

  try {
    isGenerating.value = true

    // create anonymous user
    if (!user.value) {
      const { data, error } = await supabase.auth.signInAnonymously()
      console.log('Anonymous user created:', data, 'Error:', error)
    }

    await $fetch('/api/images/generate', {
      method: 'post',
      server: false,
      body: {
        prompt: prompt.value,
        model: modelName,
        width: width,
        height: height,
        images: imageNumber,
        style: 'None'
      }
    })

    analyticsHelper.trackEvent('image_generated', { model: modelName })

    useRouter().push('/images')
  } catch (error) {
    $toast.error('Error generating image, please try again later.')
  } finally {
    isGenerating.value = false
  }
}
</script>

<template>
  <div class="grid gap-3">
    <Input v-model="prompt" type="text" class="h-20 px-8 dark:bg-input/50" placeholder="Describe your vision..." required />
  </div>
  <div class="flex items-center justify-center mt-6">
    <Button v-if="isGenerating" @click="generateImage()" class="w-48 flex gap-2" variant="main" :disabled="true">
      <Loader2 class="w-4 h-4 animate-spin" /> Starting...
    </Button>
    <Button v-else @click="generateImage()" class="w-48" variant="main" size="lg"> Generate </Button>
  </div>
</template>
