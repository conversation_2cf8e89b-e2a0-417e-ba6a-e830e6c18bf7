<script setup lang="ts">
import { Brush, Image, ImageUpscale, Paintbrush, SquareUserRound } from 'lucide-vue-next'

import { type SidebarProps } from '@/components/ui/sidebar'

const props = withDefaults(defineProps<SidebarProps>(), {})

const route = useRoute()
const settingsStore = useSettings()

const mainNav = computed(() => [
  {
    title: 'Create',
    action: () => {
      settingsStore.mode = 'create'
      navigateTo('/images')
    },
    icon: Paintbrush,
    isActive: settingsStore.mode === 'create' && route.path.startsWith('/images')
  },
  {
    title: 'Upscale',
    action: () => {
      if (!settingsStore.devMode) return

      settingsStore.mode = 'upscale'
      navigateTo('/images')
    },
    icon: ImageUpscale,
    isActive: settingsStore.mode === 'upscale' && route.path.startsWith('/images'),
    isDisabled: !settingsStore.devMode
  },
  {
    title: 'Inpaint',
    action: () => {
      if (!settingsStore.devMode) return

      navigateTo('/inpaint')
    },
    icon: Brush,
    isActive: settingsStore.mode === 'inpaint' && route.path.startsWith('/images'),
    isDisabled: true
  },
  {
    title: 'Discover',
    action: () => {
      if (!settingsStore.devMode) return

      navigateTo('/discover')
    },
    icon: Image,
    isActive: route.path === '/discover',
    isDisabled: true
  }
])
</script>

<template>
  <Sidebar v-bind="props">
    <SidebarHeader class="mx-2">
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton size="lg" as-child>
            <AppLogo></AppLogo>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarHeader>
    <SidebarContent class="mx-2">
      <NavMain :items="mainNav" class="mt-4" />
      <NavFilters />
      <NavFolders />
    </SidebarContent>
    <SidebarFooter class="mx-2">
      <NavUser />
    </SidebarFooter>
  </Sidebar>
</template>
