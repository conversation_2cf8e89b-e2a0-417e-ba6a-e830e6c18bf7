<script setup lang="ts">
import { cn } from '@/lib/utils'
import type { HTMLAttributes } from 'vue'

const config = useRuntimeConfig()
const supabase = useSupabaseClient()
const user = useSupabaseUser()

const props = defineProps<{
  class?: HTMLAttributes['class']
}>()

const emit = defineEmits(['emailSent'])

const isLinkingAccount = computed(() => user?.value?.is_anonymous)

const email = ref('')
async function signInWithEmail() {
  const { data, error } = await supabase.auth.signInWithOtp({
    email: email.value,
    options: {
      // set this to false if you do not want the user to be automatically signed up
      shouldCreateUser: true,
      emailRedirectTo: `${config.public.appUrl}/confirm`
    }
  })

  if (!error) {
    emit('emailSent')
  }
}

async function signInWithOAuth(provider: 'google' | 'twitter' | 'discord') {
  if (isLinkingAccount.value) {
    const { data, error } = await supabase.auth.linkIdentity({
      provider: provider
    })
  } else {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: provider,
      options: {
        redirectTo: `${config.public.appUrl}/confirm`
      }
    })
  }
}
</script>

<template>
  <div class="flex flex-col gap-3">
    <Card :class="cn(props.class)">
      <CardHeader class="flex justify-center pb-5">
        <AppLogo />
      </CardHeader>
      <CardContent class="">
        <div v-if="user?.is_anonymous" class="border-l-4 border-emerald-400 bg-emerald-500/20 p-3 mb-8">
          <div class="flex items-center">
            <Icon name="lucide:user" class="shrink-0 text-4xl text-emerald-400" aria-hidden="true" />
            <div class="ml-3">
              <p class="text-base text-emerald-500">
                You are almost there. Link a social account or email to unlock your collection and enjoy daily AI credits.
              </p>
            </div>
          </div>
        </div>

        <div class="grid gap-6">
          <div class="flex flex-col gap-4">
            <Button @click="signInWithOAuth('google')" variant="outline" class="w-full">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="-0.5 0 48 48" class="size-6 mr-1">
                <g id="Icons" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                  <g id="Color-" transform="translate(-401.000000, -860.000000)">
                    <g id="Google" transform="translate(401.000000, 860.000000)">
                      <path
                        d="M9.********,24 C9.********,22.4757333 10.0804318,21.0144 10.5322727,19.6437333 L2.********,13.6042667 C1.********,16.7338667 0.*********,20.2602667 0.*********,24 C0.*********,27.7365333 1.081,31.2608 2.62025,34.3882667 L10.5247955,28.3370667 C10.0772273,26.9728 9.********,25.5168 9.********,24"
                        id="Fill-1"
                        fill="#FBBC05"
                      ></path>
                      <path
                        d="M23.7136364,10.1333333 C27.025,10.1333333 30.0159091,11.3066667 32.3659091,13.2266667 L39.2022727,6.4 C35.0363636,2.77333333 29.6954545,0.533333333 23.7136364,0.533333333 C14.4268636,0.533333333 6.44540909,5.84426667 2.********,13.6042667 L10.5322727,19.6437333 C12.3545909,14.112 17.5491591,10.1333333 23.7136364,10.1333333"
                        id="Fill-2"
                        fill="#EB4335"
                      ></path>
                      <path
                        d="M23.7136364,37.8666667 C17.5491591,37.8666667 12.3545909,33.888 10.5322727,28.3562667 L2.********,34.3946667 C6.44540909,42.1557333 14.4268636,47.4666667 23.7136364,47.4666667 C29.4455,47.4666667 34.9177955,45.4314667 39.0249545,41.6181333 L31.5177727,35.8144 C29.3995682,37.1488 26.7323182,37.8666667 23.7136364,37.8666667"
                        id="Fill-3"
                        fill="#34A853"
                      ></path>
                      <path
                        d="M46.1454545,24 C46.1454545,22.6133333 45.9318182,21.12 45.6113636,19.7333333 L23.7136364,19.7333333 L23.7136364,28.8 L36.3181818,28.8 C35.6879545,31.8912 33.9724545,34.2677333 31.5177727,35.8144 L39.0249545,41.6181333 C43.3393409,37.6138667 46.1454545,31.6490667 46.1454545,24"
                        id="Fill-4"
                        fill="#4285F4"
                      ></path>
                    </g>
                  </g>
                </g>
              </svg>
              Login with Google
            </Button>

            <Button @click="signInWithOAuth('twitter')" variant="outline" class="w-full">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" class="text-white size-7 mr-1">
                <path
                  d="M64 32C28.7 32 0 60.7 0 96V416c0 35.3 28.7 64 64 64H384c35.3 0 64-28.7 64-64V96c0-35.3-28.7-64-64-64H64zm297.1 84L257.3 234.6 379.4 396H283.8L209 298.1 123.3 396H75.8l111-126.9L69.7 116h98l67.7 89.5L313.6 116h47.5zM323.3 367.6L153.4 142.9H125.1L296.9 367.6h26.3z"
                  fill="currentColor"
                />
              </svg>
              Login with X
            </Button>

            <Button @click="signInWithOAuth('discord')" variant="outline" class="w-full">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512" class="size-7 text-[#5865F2]">
                <path
                  d="M524.5 69.8a1.5 1.5 0 0 0 -.8-.7A485.1 485.1 0 0 0 404.1 32a1.8 1.8 0 0 0 -1.9 .9 337.5 337.5 0 0 0 -14.9 30.6 447.8 447.8 0 0 0 -134.4 0 309.5 309.5 0 0 0 -15.1-30.6 1.9 1.9 0 0 0 -1.9-.9A483.7 483.7 0 0 0 116.1 69.1a1.7 1.7 0 0 0 -.8 .7C39.1 183.7 18.2 294.7 28.4 404.4a2 2 0 0 0 .8 1.4A487.7 487.7 0 0 0 176 479.9a1.9 1.9 0 0 0 2.1-.7A348.2 348.2 0 0 0 208.1 430.4a1.9 1.9 0 0 0 -1-2.6 321.2 321.2 0 0 1 -45.9-21.9 1.9 1.9 0 0 1 -.2-3.1c3.1-2.3 6.2-4.7 9.1-7.1a1.8 1.8 0 0 1 1.9-.3c96.2 43.9 200.4 43.9 295.5 0a1.8 1.8 0 0 1 1.9 .2c2.9 2.4 6 4.9 9.1 7.2a1.9 1.9 0 0 1 -.2 3.1 301.4 301.4 0 0 1 -45.9 21.8 1.9 1.9 0 0 0 -1 2.6 391.1 391.1 0 0 0 30 48.8 1.9 1.9 0 0 0 2.1 .7A486 486 0 0 0 610.7 405.7a1.9 1.9 0 0 0 .8-1.4C623.7 277.6 590.9 167.5 524.5 69.8zM222.5 337.6c-29 0-52.8-26.6-52.8-59.2S193.1 219.1 222.5 219.1c29.7 0 53.3 26.8 52.8 59.2C275.3 311 251.9 337.6 222.5 337.6zm195.4 0c-29 0-52.8-26.6-52.8-59.2S388.4 219.1 417.9 219.1c29.7 0 53.3 26.8 52.8 59.2C470.7 311 447.5 337.6 417.9 337.6z"
                  fill="currentColor"
                />
              </svg>
              Login with Discord
            </Button>
          </div>
          <div
            class="after:border-border relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t"
          >
            <span class="bg-card text-muted-foreground relative z-10 px-2"> Or continue with </span>
          </div>
          <div class="grid gap-6">
            <div class="grid gap-3">
              <Label for="email">Email</Label>
              <Input v-model="email" id="email" type="email" placeholder="<EMAIL>" required />
            </div>
            <Button @click="signInWithEmail()" class="w-full" :disabled="!email"> Login </Button>
          </div>
        </div>
      </CardContent>
    </Card>
    <div class="text-muted-foreground *:[a]:hover:text-primary text-center text-xs text-balance *:[a]:underline *:[a]:underline-offset-4">
      By clicking continue, you agree to our <NuxtLink to="/terms">Terms of Service</NuxtLink> and <NuxtLink to="/privacy">Privacy Policy</NuxtLink>.
    </div>
  </div>
</template>
