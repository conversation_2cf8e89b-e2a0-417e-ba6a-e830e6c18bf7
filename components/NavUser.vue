<script setup lang="ts">
import { CircleFadingArrowUp, Ellipsis, MessageCircleMore, Newspaper, Power, Shield } from 'lucide-vue-next'
import { useSidebar } from '@/components/ui/sidebar'
import { useTimeAgo } from '@vueuse/core'

const { isMobile } = useSidebar()
const user: any = useSupabaseUser()

const isGuest = computed(() => !user.value.email)
const visibleName = computed(() => (user.value.email ? user.value.email.split('@')[0] : user.value.id))

const accountStore = useAccount()
const timeToCreditReset = computed(() => {
  if (!accountStore.account?.reset_at) return null

  const nextResetUnix = new Date(accountStore.account.reset_at).getTime()
  const nextResetDate = new Date(nextResetUnix + 1000 * 60 * 60 * 24)
  return useTimeAgo(nextResetDate, { updateInterval: 60000 })
})
</script>

<template>
  <SidebarMenu>
    <SidebarMenuItem v-if="accountStore.account" class="px-1">
      <Card class="shadow-none rounded-md py-0">
        <CardHeader class="px-4 py-2 flex items-center justify-between gap-0.5">
          <CardTitle class="text-xs"> Credits: {{ accountStore.account.daily_credits }} </CardTitle>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger><Icon name="lucide:timer-reset" class="font-semibold text-muted-foreground align-middle" /></TooltipTrigger>
              <TooltipContent :sideOffset="0">
                <p>
                  Daily Reset: <span class="capitalize font-semibold">{{ timeToCreditReset }}</span>
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </CardHeader>
      </Card>
    </SidebarMenuItem>
    <SidebarMenuItem>
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <SidebarMenuButton size="lg" class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground">
            <Avatar class="h-8 w-8 rounded-lg">
              <AvatarImage v-if="user.avatar" :src="user.avatar" :alt="visibleName" />
              <AvatarFallback class="rounded-lg"> {{ visibleName?.substring(0, 2) || '' }} </AvatarFallback>
            </Avatar>
            <div class="grid flex-1 text-left text-sm leading-tight">
              <span v-if="isGuest" class="truncate font-medium">Guest</span>
              <span v-else class="truncate font-medium">{{ visibleName }}</span>
            </div>
            <Ellipsis class="ml-auto size-4" />
          </SidebarMenuButton>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          class="w-[--reka-dropdown-menu-trigger-width] min-w-56 rounded-lg"
          :side="isMobile ? 'bottom' : 'right'"
          align="end"
          :side-offset="4"
        >
          <DropdownMenuLabel class="p-0 font-normal">
            <div class="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
              <Avatar class="h-8 w-8 rounded-lg">
                <AvatarImage v-if="user.avatar" :src="user.avatar" :alt="visibleName" />
                <AvatarFallback class="rounded-lg"> {{ visibleName?.substring(0, 2) || '' }} </AvatarFallback>
              </Avatar>
              <div class="grid flex-1 text-left text-sm leading-tight">
                <span v-if="isGuest" class="truncate font-semibold">Guest</span>
                <span v-else class="truncate font-semibold">{{ visibleName }}</span>
              </div>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            <DropdownMenuItem @click="navigateTo('/upgrade')">
              <CircleFadingArrowUp />
              Upgrade
            </DropdownMenuItem>
          </DropdownMenuGroup>
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            <DropdownMenuItem @click="navigateTo('/terms')"><Newspaper /> Terms & Conditions </DropdownMenuItem>
            <DropdownMenuItem @click="navigateTo('/privacy')"><Shield /> Privacy Policy </DropdownMenuItem>
            <DropdownMenuItem
              @click="
                navigateTo('https://discord.gg/ZcPkvRWJQd', {
                  open: { target: '_blank' }
                })
              "
            >
              <MessageCircleMore />
              Join Discord
            </DropdownMenuItem>
          </DropdownMenuGroup>
          <DropdownMenuSeparator />
          <DropdownMenuItem @click="navigateTo('/logout')">
            <Power />
            Log out
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </SidebarMenuItem>
  </SidebarMenu>
</template>
