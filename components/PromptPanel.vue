<script setup lang="ts">
import { createReusableTemplate, useMediaQuery } from '@vueuse/core'
import { Check, ChevronsUpDown, Delete, Loader2, Search } from 'lucide-vue-next'
import { defaultModel, maxPromptLength, models, styles } from '~/config/venice.config'
import { presets, type PromptTemplate } from '~/config/prompts.config'

const [SettingsTemplate, SettingsPanel] = createReusableTemplate()

const isDesktop = useMediaQuery('(min-width: 768px)')
const route = useRoute()

const settingsStore = useSettings()

const modelGroups = Object.values(models).reduce((acc: any, model) => {
  const group = model.name.split('@')[0]

  if (!acc[group]) {
    acc[group] = {
      name: group,
      label: model.label,
      description: model.description,
      cover: model.cover,
      models: []
    }
  }

  acc[group].models.push(model)

  return acc
}, {})

const prompt = ref('')
const promptHistory = ref([] as string[])
const model = ref({ ...defaultModel })
const aspectRatio = ref(model.value.aspectRatio[0])
const style = ref('None')
const imageNumber = ref(1)

const lastPreset = ref(null as PromptTemplate | null)
const selectedFile = ref<File | null>(null)
const imagePreview = ref<string | null>(null)

watch(model, () => {
  if (!model.value.aspectRatio.find((r) => r.name === aspectRatio.value.name)) {
    aspectRatio.value = model.value.aspectRatio[0]
  }
})

const mode = computed(() => settingsStore.mode)

const applyPreset = (preset: PromptTemplate) => {
  if (preset.model && !models[preset.model]) {
    $toast.error('The model specified by this preset is not available anymore. Please select a different preset.')
    return
  }

  lastPreset.value = { ...preset }

  // store current prompt
  if (preset.prompt) {
    storePrompt()
  }

  if (preset.prompts?.length) {
    prompt.value = preset.prompts.sort(() => 0.5 - Math.random())[0]
  } else if (preset.prompt) {
    prompt.value = preset.prompt
  }

  if (preset.model) {
    model.value = { ...models[preset.model] }
  }

  if (preset.style) {
    style.value = preset.style
  }

  if (preset.aspectRatio) {
    aspectRatio.value = model.value.aspectRatio.find((r) => r.name === preset.aspectRatio)!
  }
}

const triggerGeneration = (params: { prompt: string; model: string; style: string; folderId: string; width?: number; height?: number }) => {
  if (!params.prompt) {
    $toast.error('Cannot find a valid prompt to re-generate this image.')
    return
  }
  if (!models[params.model]) {
    $toast.error('The model used to create this image is not available anymore. You can still copy the prompt and generate a new image with another model.')
    return
  }

  // store current prompt
  storePrompt()

  // set values
  prompt.value = params.prompt
  model.value = { ...models[params.model] }
  style.value = params.style || 'None'
  // adjust aspect ratio if possible
  const newAspectRatio = model.value.aspectRatio.find((r) => r.width === params.width && r.height === params.height)
  if (newAspectRatio) {
    aspectRatio.value = newAspectRatio
  }

  // always 1 image on re-generate
  imageNumber.value = 1

  // generate image
  const newImageSizeParams = newAspectRatio ? { width: newAspectRatio.width, height: newAspectRatio.height } : { width: params.width, height: params.height }
  const newImageParams = {
    ...newImageSizeParams,
    ...(params?.folderId ? { folderId: params.folderId } : {})
  }
  generateImage(newImageParams)
}

const isUpdating = computed(() => isGenerating.value || isEnhancing.value)

const isGenerating = ref(false)
const generateImage = async (params?: { folderId?: string; width?: number; height?: number }) => {
  if (isGenerating.value) return

  if (prompt.value.length < 15) {
    $toast.error(`Prompt is too short. Try to describe more details of the image you want to generate.`)
    return
  }
  if (prompt.value.length > maxPromptLength) {
    $toast.error(`Prompt is too long. Please keep it under ${maxPromptLength} characters.`)
    return
  }

  try {
    isGenerating.value = true

    const width = params?.width || aspectRatio.value.width
    const height = params?.height || aspectRatio.value.height

    const routeFolderId = route.params.folder ? `${route.params.folder}` : null

    await $fetch('/api/images/generate', {
      method: 'post',
      body: {
        prompt: prompt.value,
        model: model.value.name,
        width: width,
        height: height,
        images: imageNumber.value,
        style: style.value || 'None',
        ...(routeFolderId ? { folder: routeFolderId } : params?.folderId ? { folder: params.folderId } : {})
      }
    })

    // save in history
    storePrompt()

    $toast.success('Image generation started...')

    analyticsHelper.trackEvent('image_generated', { model: model.value.name })
  } catch (error) {
    $toast.error('Error generating image, please try again later.')
  } finally {
    isGenerating.value = false
  }
}

const storePrompt = () => {
  // save in history
  if (promptHistory.value.includes(prompt.value)) {
    return
  }

  promptHistory.value.unshift(prompt.value)

  if (promptHistory.value.length > 20) {
    promptHistory.value.pop()
  }
}
const undoPrompt = () => {
  if (!promptHistory.value.length) {
    return
  }

  prompt.value = promptHistory.value.shift()!
}

const isEnhancing = ref(false)
const enhancePrompt = async (style?: string) => {
  if (isEnhancing.value) return

  try {
    isEnhancing.value = true

    const result: any = await $fetch('/api/enhance', {
      method: 'post',
      body: {
        prompt: prompt.value,
        style: style
      }
    })

    if (!result.success || !result.prompt || result.prompt.length < 50) {
      throw new Error('Invalid prompt returned')
    }

    // store old prompt
    storePrompt()

    // replace prompt
    prompt.value = result.prompt

    analyticsHelper.trackEvent('prompt_enhanced')
  } catch (error) {
    $toast.error('Error enhancing prompt, please try again later.')
  } finally {
    isEnhancing.value = false
  }
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (!file) return

  // Check if file is an image
  if (!file.type.startsWith('image/')) {
    $toast.error('Please select an image file.')
    return
  }

  selectedFile.value = file

  // Create preview URL
  const reader = new FileReader()
  reader.onload = (e) => {
    imagePreview.value = e.target?.result as string
  }
  reader.readAsDataURL(file)
}

defineExpose({
  triggerGeneration
})
</script>

<template>
  <SettingsTemplate>
    <DropdownMenu>
      <DropdownMenuTrigger as-child>
        <div class="max-md:col-span-full flex flex-col gap-0.5">
          <Label for="model" class="text-muted-foreground text-[0.675rem] uppercase ml-0.5">Model</Label>
          <Button variant="outline" id="model" class="text-xs uppercase xl:w-56 flex justify-between items-center">
            {{ model.label || model.name }} <ChevronsUpDown class="opacity-70"></ChevronsUpDown>
          </Button>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" class="w-86 lg:w-90">
        <template v-for="(group, groupName, index) in modelGroups" :key="groupName">
          <DropdownMenuLabel class="flex items-center gap-3 py-2 px-2 lg:px-4">
            <div class="size-16 min-w-16">
              <UnLazyImage :src="group.cover" class="size-16 rounded aspect-square object-cover"></UnLazyImage>
            </div>
            <div class="leading-6">
              <div class="font-semibold font-display">{{ group.label }}</div>
              <small class="text-xs text-muted-foreground text-pretty leading-4.5 line-clamp-2">{{ group.description }}</small>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuGroup class="flex items-center gap-2 pb-2 px-2 lg:px-4">
            <DropdownMenuItem
              v-for="modelConfig in group.models"
              :key="modelConfig.name"
              @click="model = { ...modelConfig }"
              :class="[
                'grow h-auto flex flex-col justify-center text-xs capitalize leading-1',
                'px-5',
                'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50'
              ]"
            >
              <span class="font-display text-xs leading-none">{{ modelConfig.name.split('@')[1] || 'standard' }}</span>
              <span class="font-sans text-2xs text-pink-500 border-pink-500 uppercase leading-none">{{ modelConfig.credits }} credit</span>
            </DropdownMenuItem>
          </DropdownMenuGroup>
          <DropdownMenuSeparator v-if="index !== Object.keys(modelGroups).length - 1" />
        </template>
      </DropdownMenuContent>
    </DropdownMenu>

    <DropdownMenu>
      <DropdownMenuTrigger as-child>
        <div class="flex flex-col gap-0.5">
          <Label for="ratio" class="text-muted-foreground text-[0.675rem] uppercase ml-0.5">Aspect Ratio</Label>
          <Button variant="outline" id="ratio" class="text-xs uppercase xl:w-42 flex justify-between items-center">
            {{ aspectRatio.name }} <ChevronsUpDown class="opacity-70"></ChevronsUpDown>
          </Button>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent class="w-56" align="start" side="top">
        <DropdownMenuItem
          v-for="aspectRatioConfig in model.aspectRatio"
          :key="aspectRatioConfig.name"
          @click="aspectRatio = { ...aspectRatioConfig }"
          class="py-2 pl-4 pr-1.5 flex items-center justify-between"
        >
          <span>{{ aspectRatioConfig.name }}</span>
          <DropdownMenuShortcut class="text-[0.675rem]">({{ aspectRatioConfig.ratio }})</DropdownMenuShortcut>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>

    <Combobox v-model="style" by="label" class="flex flex-col gap-0.5">
      <Label for="style" class="text-muted-foreground text-[0.675rem] uppercase ml-0.5">Style</Label>
      <ComboboxAnchor as-child>
        <ComboboxTrigger as-child>
          <Button id="style" variant="outline" class="text-xs uppercase w-auto xl:w-48 flex justify-between items-center">
            {{ style ?? 'None' }} <ChevronsUpDown class="opacity-70"></ChevronsUpDown>
          </Button>
        </ComboboxTrigger>
      </ComboboxAnchor>

      <ComboboxList
        class="w-full grid gap-3 p-4 md:w-[520px] md:grid-cols-4 lg:w-[640px] 2xl:grid-cols-5 2xl:w-[960px]"
        @focusOutside.prevent
        :disableOutsidePointerEvents="true"
      >
        <div class="w-full flex items-center gap-3">
          <div class="grow relative w-full flex items-center order-last md:order-first" style="width: 100%; flex-grow: 1">
            <ComboboxInput
              class="w-full grow pl-2 focus-visible:ring-0 border-0 rounded-none h-10"
              style="width: 100%; flex-grow: 1"
              placeholder="Select style..."
            />
            <span class="absolute start-0 inset-y-0 flex items-center justify-center px-3">
              <Search class="size-4 text-muted-foreground" />
            </span>
          </div>
          <ComboboxItem :value="'None'" class="text-accent-foreground px-3"><Delete class="size-5" /></ComboboxItem>
        </div>

        <ComboboxEmpty> No style found. </ComboboxEmpty>

        <ComboboxGroup class="grid gap-3 md:grid-cols-4 max-h-100 overflow-y-auto">
          <ComboboxItem v-for="styleName in styles" :key="styleName" :value="styleName" class="py-0.5">
            <Avatar class="size-14 2xl:size-18 border border-muted rounded aspect-square object-cover">
              <AvatarImage :src="`/images/styles/${styleName}.webp`" :alt="styleName" class="aspect-square object-cover" />
              <AvatarFallback class="rounded bg-slate-700/20"> {{ styleName.substring(0, 2) }} </AvatarFallback>
            </Avatar>
            <span class="font-semibold">
              {{ styleName }}
            </span>

            <ComboboxItemIndicator>
              <Check class="ml-auto h-4 w-4" />
            </ComboboxItemIndicator>
          </ComboboxItem>
        </ComboboxGroup>
      </ComboboxList>
    </Combobox>
  </SettingsTemplate>

  <div class="flex flex-col gap-0">
    <div class="flex items-center justify-between gap-3 mb-2">
      <Label v-if="mode === 'upscale'" for="prompt" class="text-xs font-display">{{ 'Upload an image to upscale' }}</Label>
      <Label v-else for="prompt" class="text-xs font-display">{{ isDesktop ? 'Describe your vision with a prompt' : 'Describe your vision' }}</Label>
      <div class="flex gap-2">
        <Button @click="undoPrompt()" variant="outline" size="sm" :disabled="!promptHistory.length">
          <Icon name="lucide:undo-2" class="text-lg" />
        </Button>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger
              ><Button @click="enhancePrompt()" variant="outline" size="sm" :disabled="isUpdating || !prompt || prompt.length < 10"
                ><span class="hidden lg:inline mr-1.5">Enhance</span> <Icon name="lucide:wand-sparkles" class="text-lg" /></Button
            ></TooltipTrigger>
            <TooltipContent :sideOffset="0">
              <p>Use AI to enhance your prompt</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </div>

    <div class="flex items-start gap-3">
      <div v-if="mode === 'upscale'" class="size-40 relative">
        <Button v-if="!imagePreview" as-child class="size-40">
          <label for="file-input" class="cursor-pointer">
            <Icon name="lucide:image-up" class="text-4xl text-muted-foreground" />
          </label>
        </Button>
        <div v-else class="size-40 relative group cursor-pointer">
          <label for="file-input" class="block size-full">
            <img
              :src="imagePreview"
              class="size-40 rounded object-cover border-2 border-dashed border-muted-foreground hover:border-foreground transition-colors"
            />
            <div class="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded flex items-center justify-center">
              <Icon name="lucide:image-up" class="text-3xl text-white" />
            </div>
          </label>
        </div>

        <Input id="file-input" type="file" accept="image/*" class="hidden" @change="handleFileSelect" />
      </div>

      <Textarea
        id="prompt"
        v-model="prompt"
        class="resize-none min-h-20"
        :class="[{ 'cursor-not-allowed animate-pulse': isEnhancing }, { 'h-40': mode === 'upscale' }]"
        placeholder="Write your prompt here..."
        required
        :readonly="isUpdating"
      />
    </div>

    <div v-if="mode === 'create'" class="hidden lg:flex lg:flex-wrap items-center gap-2 mt-1">
      <div class="text-muted-foreground text-[0.675rem]">Presets:</div>
      <Badge
        v-for="preset in presets"
        :key="preset.name"
        variant="outline"
        class="py-0 px-5 cursor-pointer border-violet-500/30 hover:border-violet-500/50"
        :class="lastPreset?.name === preset.name ? 'border-violet-500 bg-violet-500/60 text-white' : ''"
        @click="applyPreset(preset)"
        >{{ preset.name }}</Badge
      >
    </div>
  </div>

  <div v-if="mode === 'upscale'" class="flex justify-end gap-2 lg:gap-4">
    <Button v-if="isGenerating" class="w-32 lg:w-48 flex gap-2" variant="main" :disabled="true"> <Loader2 class="w-4 h-4 animate-spin" /> Starting... </Button>
    <Button v-else @click="generateImage()" class="w-32 lg:w-48" variant="main" :disabled="!prompt || isUpdating"> Upscale </Button>
  </div>
  <div v-else class="flex items-end gap-2 lg:gap-4">
    <SettingsPanel v-if="isDesktop"></SettingsPanel>
    <Drawer v-else>
      <DrawerTrigger as-child>
        <Button variant="outline"> <Icon name="lucide:settings" /> </Button>
      </DrawerTrigger>
      <DrawerContent>
        <div class="grid grid-cols-2 gap-x-2 gap-y-5 p-4">
          <SettingsPanel></SettingsPanel>
        </div>
        <DrawerFooter class="pt-2">
          <DrawerClose as-child>
            <Button variant="outline"> Close </Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>

    <div class="grow"></div>

    <NumberField v-model.number="imageNumber" :default-value="1" :min="1" :max="4" class="flex flex-col gap-0.5">
      <Label for="image-number" class="text-muted-foreground text-[0.675rem] uppercase ml-0.5">Images</Label>
      <NumberFieldContent id="image-number" class="w-28">
        <NumberFieldDecrement />
        <NumberFieldInput />
        <NumberFieldIncrement />
      </NumberFieldContent>
    </NumberField>
    <Button v-if="isGenerating" class="w-32 lg:w-48 flex gap-2" variant="main" :disabled="true"> <Loader2 class="w-4 h-4 animate-spin" /> Starting... </Button>
    <Button v-else @click="generateImage()" class="w-32 lg:w-48" variant="main" :disabled="!prompt || isUpdating"> Generate </Button>
  </div>
</template>
