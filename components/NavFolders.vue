<script setup lang="ts">
import { Folder, Database, SquarePlus, Trash2 } from 'lucide-vue-next'

const user = useSupabaseUser()

const foldersStore = useFolders()

const allowedColors = [
  'text-slate-300',
  'text-blue-300',
  'text-indigo-300',
  'text-purple-300',
  'text-pink-300',
  'text-red-300',
  'text-orange-300',
  'text-yellow-300',
  'text-green-300',
  'text-teal-300',
  'text-cyan-300'
]

await foldersStore.refresh()

const newFolderModalOpen = ref(false)
const newFolderModalName = ref('')
const newFolderModalColor = ref('slate-300')
const createFolder = async () => {
  if (!user.value) return
  if (!newFolderModalName.value) return

  await foldersStore.create(newFolderModalName.value, newFolderModalColor.value)

  newFolderModalOpen.value = false
  newFolderModalName.value = ''
}
const deleteFolder = async (folderId: string) => {
  if (!user.value) return

  await foldersStore.delete(folderId)
}
</script>

<template>
  <SidebarGroup class="group-data-[collapsible=icon]:hidden">
    <SidebarGroupLabel class="relative justify-between items-center">
      My Folders
      <SidebarMenuAction @click="newFolderModalOpen = true" class="cursor-pointer">
        <SquarePlus />
        <span class="sr-only">Add</span>
      </SidebarMenuAction>
    </SidebarGroupLabel>
    <SidebarMenu>
      <SidebarMenuItem>
        <SidebarMenuButton as-child>
          <NuxtLink :to="`/images`">
            <Database />
            <span>All Images</span>
          </NuxtLink>
        </SidebarMenuButton>
      </SidebarMenuItem>
      <SidebarMenuItem v-for="folder in foldersStore.folders" :key="folder.name">
        <SidebarMenuButton as-child :is-active="$route.params.folder === folder.id">
          <NuxtLink :to="`/images/${folder.id}`">
            <Folder :class="folder.color || ''" />
            <span>{{ folder.name }}</span>
          </NuxtLink>
        </SidebarMenuButton>
        <SidebarMenuAction show-on-hover @click="deleteFolder(folder.id)" class="cursor-pointer">
          <Trash2 class="text-sidebar-foreground/50" />
          <span class="sr-only">Del</span>
        </SidebarMenuAction>
      </SidebarMenuItem>

      <SidebarMenuItem v-if="!foldersStore.folders?.length" class="mt-2">
        <SidebarMenuButton as-child>
          <Button @click="newFolderModalOpen = true" variant="outline" class="text-xs">Create</Button>
        </SidebarMenuButton>
      </SidebarMenuItem>
    </SidebarMenu>

    <Dialog :open="newFolderModalOpen" @update:open="newFolderModalOpen = $event">
      <DialogContent class="max-w-7xl">
        <DialogHeader>
          <DialogTitle>New folder</DialogTitle>
          <DialogDescription> Create a new folder to organize your images. </DialogDescription>
        </DialogHeader>

        <div class="grid gap-3">
          <Label for="folder">Name</Label>
          <div class="flex items-center gap-3">
            <Input v-model="newFolderModalName" id="folder" type="text" placeholder="Folder name" required />
            <DropdownMenu>
              <DropdownMenuTrigger as-child>
                <Button variant="outline"><Folder :class="newFolderModalColor" /></Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent class="w-56">
                <DropdownMenuGroup class="flex flex-wrap justify-center items-center gap-3">
                  <DropdownMenuItem v-for="color in allowedColors" :key="color" @click="newFolderModalColor = color">
                    <Folder class="size-5" :class="color" />
                  </DropdownMenuItem>
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <DialogFooter>
          <Button @click="newFolderModalOpen = false" variant="outline"> Close </Button>
          <Button @click="createFolder()" variant="default" :disabled="!newFolderModalName"> Create </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </SidebarGroup>
</template>
