import type { VeniceModels } from './venice.config'

export type PromptTemplate = {
  name: string
  prompt?: string
  prompts?: string[]
  model?: VeniceModels
  style?: string
  aspectRatio: string
}

export const presets: PromptTemplate[] = [
  {
    name: 'App Icon',
    prompt: 'Minimalist app icon of a [OBJECT/CONCEPT] in [COLOR] color with [DOMINANT_COLOR] background, flat vector style, centered design, high contrast',
    model: 'hidream',
    style: 'Minimalist',
    aspectRatio: 'Square'
  },
  {
    name: 'Studio Ghibli',
    prompt:
      'A scene with [CHARACTER] in a [NATURE_SETTING], drawn in the style of a Studio Ghibli animation, soft lighting, warm tones, with a warm, nostalgic feel.',
    model: 'venice-sd35',
    style: 'Anime',
    aspectRatio: 'Cinema'
  },
  {
    name: 'Action Figure',
    prompt: `Make a picture of a 3D action figure toy, named "[<PERSON>AM<PERSON>]" Make it look like it's being displayed in a transparent plastic package, blister packaging model. The figure is as in the photo, [GENDER] style is very [DESCRIBE EVERYTHING ABOUT HAIR/FACE/ETC]. On the top of the packaging there is a large writing: "[NAME]" in white text. Dressed in [CLOTHING/ACCESSORIES]. Also add some supporting items for the job next to the figure, like [ALL-THE-THINGS]. The packaging design is minimalist, cardboard color, cute toy style sold in stores. The style is cartoonish, cute but still neat.`,
    model: 'flux-dev-uncensored-11',
    style: '3D Model',
    aspectRatio: 'Square'
  },
  {
    name: 'Desktop Wallpaper',
    prompts: [
      'A dense pine forest at dawn, with golden light filtering through the mist, soft painterly strokes illustration, gentle cool hues blending with warm light, desktop wallpaper format, inspired by traditional landscape painting',
      'A wide desert canyon under a glowing sunset sky, orange and purple tones reflected on layered rock formations, painterly style illustration with texture-rich brushwork and subtle atmospheric depth, designed for desktop wallpaper',
      'A still alpine lake surrounded by autumn trees in full color, reflections in the water, soft clouds drifting over snow-tipped peaks, warm and earthy palette, illustration in artistic style, wallpaper resolution',
      'An icy tundra landscape illuminated by green and purple northern lights, minimal vegetation, cold air textures and light fog, impressionist or illustration style with wide aspect ratio',
      'A countryside scene of rolling green hills under light spring rain, scattered wildflowers, a small cottage in the distance, moody sky and soft wet textures, illustration painted in an expressive, abstract style for a desktop background',
      'A panoramic view of a modern city at twilight, glass skyscrapers reflecting the last orange light of the sunset, bustling streets below with glowing traffic and moving silhouettes, deep blue sky transitioning to night, atmospheric depth and cinematic haze, painted in a digital impressionist style with soft brush strokes and vibrant highlights, wallpaper format',
      'A lively scene in an old Chinese water town at dusk, narrow cobbled streets lit by red lanterns, wooden buildings with curved tiled roofs, townspeople in traditional attire, street food vendors with steam rising into the cool evening air, reflections shimmering on the canal, rich cultural details in a romantic, watercolor-inspired painting style, designed for desktop wallpaper'
    ],
    model: 'venice-sd35@high',
    style: 'Watercolor',
    aspectRatio: 'Cinema'
  },
  {
    name: 'Isometric Game Asset',
    prompt: 'Isometric pixel art of a [BUILDING/OBJECT], top-down view, for a [GAME_GENRE] game',
    model: 'hidream',
    style: 'Isometric Style',
    aspectRatio: 'Square'
  },
  {
    name: 'Product Mockup',
    prompt: 'Product mockup of a [PRODUCT_TYPE] placed on [surface_type] with [NATURAL_ELEMENT]',
    model: 'flux-dev-uncensored-11',
    style: 'Photographic',
    aspectRatio: 'Cinema'
  },
  {
    name: 'Minimalist Logo',
    prompt: 'Flat minimalist logo design for a [brand_type] featuring [symbol], vector image style',
    model: 'hidream',
    style: 'Minimalist',
    aspectRatio: 'Square'
  }
]

export const starterPrompts = [
  `A vivid, fiery sunset with hues of orange, pink, and red descending slowly over a majestic, rugged mountain range, casting long shadows and warm, golden light across the peaks, as the first stars begin to twinkle in the deepening twilight sky during calm, tranquil summer. Anime style with high detail look.`,
  `A stress-free tropical beach blanketed in radiant sunlight around noon. Brilliantly colored azure waves dance gently over sea-sculpted rocks near the shoreline.These even rocks emit soothing vibes. Palm trees fringed with reflective hues, gracefully sway with a soft, rhythmic breeze, casting slender shadows that dance gently upon a vivid, bright blue hammock supported by resilient ropes, inviting a sense of utter serenity and tranquility. The atmosphere is filled with the calming, rhythmic sounds of lapping waves and the sweet scent of saltwater and cocos. A few pristine white seagulls glide through the cloudless sky. Digital art style.`,
  `A majestic space view where a luminescent alien moon with shimmering violet and gold hues drifts gracefully in the foreground, casting ethereal shadows across the star-studded expanse. The moon orbits around a colossal gas giant, its surface swirling with tempestuous clouds the color of deepest ultramarine and fiery tangerine. The planet's immense gravity tugs at the moon, evoking a dynamic cosmic ballet within the boundless, twinkling night of the universe.`
]
