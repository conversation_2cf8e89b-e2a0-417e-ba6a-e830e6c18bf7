type SubscriptionType = {
  name: string
  description: string
  features: string[] | (() => string[])
  price: number
  credits: number
  retention: number
  enabled: boolean
  popular: boolean
  icon: string
}

const config: {
  [key: string]: SubscriptionType
} = {
  free: {
    name: 'Free',
    description: 'Perfect for getting started with AI image generation',
    features: ['50 daily credits', 'Access basic AI models', 'Standard quality', 'Basic organization tools'],
    price: 0,
    credits: 50,
    retention: 500,
    enabled: true,
    popular: true,
    icon: 'lucide:zap'
  },
  creator: {
    name: 'Creator',
    description: 'For serious creators who need more power and flexibility',
    features: ['500 daily credits', 'Premium AI models', 'Unlock higher quality images', 'Priority generation queue', 'Advanced organization tools'],
    price: 5.99,
    credits: 500,
    retention: 5000,
    enabled: false,
    popular: false,
    icon: 'lucide:crown'
  },
  your_own_key: {
    name: 'Bring Your Own Key',
    description: 'Use your own key and enjoy easy collection management',
    features: ['Use your own API keys', 'All the generations your key supports', 'Same storage and organization features'],
    price: 1.99,
    credits: 0,
    retention: 5000,
    enabled: false,
    popular: false,
    icon: 'lucide:key'
  }
}

export default config
