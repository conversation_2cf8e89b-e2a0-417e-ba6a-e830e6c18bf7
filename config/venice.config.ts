const defaultAspectRatio = [
  { name: 'Cinema', ratio: '16:9', width: 1280, height: 720 },
  { name: 'Square', ratio: '1:1', width: 1024, height: 1024 },
  { name: 'Landscape', ratio: '3:2', width: 1264, height: 848 },
  { name: 'Portrait', ratio: '2:3', width: 848, height: 1264 },
  { name: 'Tall', ratio: '9:16', width: 720, height: 1280 },
  { name: 'Instagram', ratio: '4:5', width: 1008, height: 1264 }
]

type ModelConfig = {
  name: string
  model: string
  label: string
  description: string
  credits: number
  params: {
    steps: number
    cfg_scale: number
    embed_exif_metadata: boolean
    safe_mode: boolean
    lora_strength?: number
  }
  aspectRatio: { name: string; ratio: string; width: number; height: number }[]
  cover?: string
}

export const models: { [key: string]: ModelConfig } = {
  'venice-sd35': {
    name: 'venice-sd35',
    model: 'venice-sd35',
    label: 'Venice SD35',
    description: 'Most artistic and creative. Get the most out of it using styles.',
    credits: 1,
    params: {
      steps: 25,
      cfg_scale: 3.5,
      embed_exif_metadata: true,
      safe_mode: true
    },
    aspectRatio: defaultAspectRatio,
    cover: 'https://images.veniceshelf.com/95e37029-8c64-4622-8e6e-fd8d3ae7ea4e/50341826-739b-4016-9bf8-957aabff2237.webp'
  },
  'venice-sd35@high': {
    name: 'venice-sd35@high',
    model: 'venice-sd35',
    label: 'Venice SD35 (High)',
    description: 'Most artistic and creative. Get the most out of it using styles.',
    credits: 2,
    params: {
      steps: 30,
      cfg_scale: 3.5,
      embed_exif_metadata: true,
      safe_mode: true
    },
    aspectRatio: defaultAspectRatio
  },
  hidream: {
    name: 'hidream',
    model: 'hidream',
    label: 'HiDream',
    description: 'Text to image and design. Great if you want to include text in your images.',
    credits: 1,
    params: {
      steps: 30,
      cfg_scale: 2,
      embed_exif_metadata: true,
      safe_mode: true
    },
    aspectRatio: defaultAspectRatio,
    cover: 'https://images.veniceshelf.com/95e37029-8c64-4622-8e6e-fd8d3ae7ea4e/75d6e2a6-6b84-4a6a-b0c8-0e8e8185a98e.webp'
  },
  'hidream@high': {
    name: 'hidream@high',
    model: 'hidream',
    label: 'HiDream (High)',
    description: 'Text to image and design. Great if you want to include text in your images.',
    credits: 2,
    params: {
      steps: 40,
      cfg_scale: 2,
      embed_exif_metadata: true,
      safe_mode: true
    },
    aspectRatio: defaultAspectRatio
  },
  'hidream@max': {
    name: 'hidream@max',
    model: 'hidream',
    label: 'HiDream (Max)',
    description: 'Text to image and design. Great if you want to include text in your images.',
    credits: 3,
    params: {
      steps: 50,
      cfg_scale: 2,
      embed_exif_metadata: true,
      safe_mode: true
    },
    aspectRatio: defaultAspectRatio
  },
  'flux-dev-uncensored-11': {
    name: 'flux-dev-uncensored-11',
    model: 'flux-dev-uncensored-11',
    label: 'FLUX Custom 1.1',
    description: 'High quality FLUX model. Good at photographic look and details.',
    credits: 1,
    params: {
      steps: 25,
      cfg_scale: 5,
      embed_exif_metadata: true,
      safe_mode: true,
      lora_strength: 75
    },
    aspectRatio: defaultAspectRatio,
    cover: 'https://images.veniceshelf.com/95e37029-8c64-4622-8e6e-fd8d3ae7ea4e/a1d6896d-2cda-4538-9eb9-bf859a0ca293.webp'
  },
  'flux-dev-uncensored-11@high': {
    name: 'flux-dev-uncensored-11@high',
    model: 'flux-dev-uncensored-11',
    label: 'FLUX Custom 1.1 (High)',
    description: 'High quality FLUX model. Good at photographic look and details.',
    credits: 2,
    params: {
      steps: 30,
      cfg_scale: 5,
      embed_exif_metadata: true,
      safe_mode: true,
      lora_strength: 75
    },
    aspectRatio: defaultAspectRatio
  }
  /*
  'lustify-sdxl': {
    name: 'lustify-sdxl',
    model: 'lustify-sdxl',
    label: 'Lustify SDXL',
    credits: 2,
    params: {
    steps: 40,
      cfg_scale: 5,
      embed_exif_metadata: true,
      safe_mode: false
    },
    aspectRatio: defaultAspectRatio
  }
  */
}

export const defaultModel = models['venice-sd35']

export type VeniceModels = keyof typeof models

export const maxPromptLength = 1500

export const styles = [
  '3D Model',
  'Abstract',
  'Advertising',
  // 'Alien',
  'Analog Film',
  'Anime',
  'Architectural',
  'Cinematic',
  // 'Collage',
  'Comic Book',
  // 'Craft Clay',
  // 'Cubist',
  'Digital Art',
  // 'Disco',
  // 'Dreamscape',
  // 'Dystopian',
  // 'Enhance',
  // 'Fairy Tale',
  'Fantasy Art',
  // 'Fighting Game',
  'Film Noir',
  // 'Flat Papercut',
  'Food Photography',
  'Gothic',
  'Graffiti',
  // 'Grunge',
  // 'GTA',
  // 'HDR',
  // 'Horror',
  // 'Hyperrealism',
  'Impressionist',
  'Isometric Style',
  // 'Kirigami',
  // 'Legend of Zelda',
  'Line Art',
  'Long Exposure',
  'Lowpoly',
  'Minecraft',
  'Minimalist',
  // 'Monochrome',
  // 'Nautical',
  // 'Neon Noir',
  'Neon Punk',
  'Origami',
  // 'Paper Mache',
  // 'Paper Quilling',
  // 'Papercut Collage',
  // 'Papercut Shadow Box',
  'Photographic',
  'Pixel Art',
  'Pointillism',
  'Pokemon',
  'Pop Art',
  // 'Psychedelic',
  // 'Real Estate',
  // 'Renaissance',
  // 'Retro Arcade',
  'Retro Game',
  // 'RPG Fantasy Game',
  // 'Silhouette',
  // 'Space',
  // 'Stacked Papercut',
  // 'Stained Glass',
  'Steampunk',
  // 'Strategy Game',
  'Street Fighter',
  'Super Mario',
  // 'Surrealist',
  // 'Techwear Fashion',
  // 'Texture',
  // 'Thick Layered Papercut',
  'Tilt-Shift',
  // 'Tribal',
  'Typography',
  'Watercolor'
  // 'Zentangle',
]

export const enhanceInstructions = `The assistant is tasked with upgrading and enhancing a user's image prompt.

RULES:
1. Only provide the edited prompt and NOTHING ELSE.
2. Do NOT include any contextual or explanatory text.
3. Do NOT prefix the prompt with any additional text.
4. The entire prompt must consist of complete sentences only.
5. The prompt must not exceed ${maxPromptLength - 100} characters.

INSTRUCTIONS:
Identify Key Elements:
- Extract the main subject(s) from the original prompt
- Note any mentioned environments, actions, or emotions

Assess Current Complexity:
- Evaluate if the prompt is too generic or lacks specificity
- Determine if it's missing contextual depth
- Enhancement Strategies

Choose one or combine multiple strategies to enhance the prompt:

1. Add Contextual Depth
Specify Time Period: Include a particular era, decade, or historical event. It can be in the past or the future
Environmental Details: Mention specific lighting conditions, weather, or unique locations
Cultural or Social Context: Incorporate elements reflecting a particular culture, or social movement

2. Introduce Action or Movement
Dynamic Verb: Replace common static verbs with more interesting dynamic verbs
Interactions: Introduce interactions between subjects or with their environment
Emotional State: Specify an emotional state to influence the subject's expression or posture

3. Incorporate Unconventional Perspectives
Unique Viewpoint: Consider specifying an unusual vantage point
Declare Style: Mandate different art styles or periods or vibes

4. Increase Specificity with Adjectives
Descriptive Adjectives: Use vivid, specific adjectives for colors, textures, and shapes
Subject Modification: Add distinctive features to your subjects`

export const enhanceStyles = {
  illustration: {
    image: '',
    stylePrompt: 'a clean, artistic illustration with defined lines, flat colors, and a creative visual flair'
  },
  anime: {
    image: '',
    stylePrompt: 'an anime-style illustration with bold outlines, vibrant colors, and expressive characters'
  },
  oil_painting: {
    image: '',
    stylePrompt: 'an oil painting style with rich textures, deep colors, and visible brushstrokes'
  },
  realism: {
    image: '',
    stylePrompt: 'a highly detailed and photorealistic style with accurate lighting and textures'
  },
  cyberpunk: {
    image: '',
    stylePrompt: 'a futuristic cyberpunk aesthetic with neon lights, rain-soaked streets, and high-tech details'
  },
  dark_fantasy: {
    image: '',
    stylePrompt: 'a dark fantasy style with gothic elements, misty atmospheres, and mythical creatures'
  },
  pixel_art: {
    image: '',
    stylePrompt: 'a retro pixel art style with low-resolution graphics and nostalgic game aesthetics'
  },
  pop_art: {
    image: '',
    stylePrompt: 'a pop art style with bold lines, bright primary colors, and comic book aesthetics'
  },
  cinematic: {
    image: '',
    stylePrompt: 'a cinematic style with dramatic lighting, wide composition, and filmic color grading'
  },
  line_art: {
    image: '',
    stylePrompt: 'a minimalist line art style with clean outlines and simple shapes'
  }
}
