import tailwindcss from '@tailwindcss/vite'
import packageJson from './package.json'

const sw = process.env.SW === 'true'

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-05-15',
  devtools: { enabled: true },
  ssr: false,

  nitro: {
    experimental: {
      tasks: true
    },
    scheduledTasks: {
      '*/10 * * * *': ['images:clear'], // run `images:clear` task every minute
      '*/2 * * * *': ['profiles:reset'] // run `images:clear` task every minute

      // TODO: migrate here the remaining tasks from supabase
    }
  },

  vite: {
    plugins: [tailwindcss()]
  },

  modules: [
    '@nuxt/content',
    '@nuxt/eslint',
    '@nuxt/fonts',
    '@nuxt/icon',
    '@nuxt/image',
    '@nuxt/ui',
    '@nuxtjs/supabase',
    '@pinia/nuxt',
    'shadcn-nuxt',
    '@unlazy/nuxt',
    '@vite-pwa/nuxt',
    (_, nuxt) => {
      nuxt.hook('pwa:beforeBuildServiceWorker', (options) => {
        console.log('pwa:beforeBuildServiceWorker: ', options.base)
      })
    }
  ],

  css: ['~/assets/css/tailwind.css', '~/assets/css/app.css'],

  pwa: {
    strategies: sw ? 'injectManifest' : 'generateSW',
    srcDir: sw ? 'service-worker' : undefined,
    filename: sw ? 'sw.ts' : undefined,
    registerType: 'autoUpdate',
    manifest: {
      name: 'VeniceShelf',
      theme_color: '#ffffff',
      icons: [
        {
          src: 'pwa-192x192.png',
          sizes: '192x192',
          type: 'image/png'
        },
        {
          src: 'pwa-512x512.png',
          sizes: '512x512',
          type: 'image/png'
        }
      ]
    }
  },

  runtimeConfig: {
    public: {
      appUrl: process.env.NUXT_APP_URL || 'https://veniceshelf.com',
      appVersion: packageJson.version,
      imageBaseUrl: 'https://images.veniceshelf.com'
    },
    // Server-side only environment variables (not exposed to the client)
    venice: {
      apiKey: process.env.NUXT_VENICE_API_KEY
    },
    cloudflare: {
      endpoint: process.env.NUXT_CLOUDFLARE_R2_ENDPOINT,
      region: process.env.NUXT_CLOUDFLARE_R2_REGION || 'auto',
      accessKeyId: process.env.NUXT_CLOUDFLARE_R2_ACCESS_KEY_ID,
      secretAccessKey: process.env.NUXT_CLOUDFLARE_R2_SECRET_ACCESS_KEY,
      bucketName: process.env.NUXT_CLOUDFLARE_R2_BUCKET_NAME
    },
    discord: {
      webhookUrl: process.env.NUXT_DISCORD_WEBHOOK_URL
    }
  },

  supabase: {
    url: process.env.SUPABASE_URL,
    key: process.env.SUPABASE_ANON_KEY,
    // ... other options
    redirectOptions: {
      login: '/login',
      callback: '/confirm',
      include: undefined,
      exclude: ['/', '/logout', '/terms', '/privacy', '/upgrade'],
      saveRedirectToCookie: false
    }
  },

  app: {
    head: {
      title: 'VeniceShelf', // default fallback title
      meta: [
        { name: 'title', content: 'VeniceShelf' },
        { name: 'og:title', content: 'VeniceShelf' },
        { name: 'twitter:title', content: 'VeniceShelf' },
        { name: 'description', content: 'AI powered platform to create stunning images and organize your collection.' },
        { name: 'og:description', content: 'AI powered platform to create stunning images and organize your collection.' },
        { name: 'twitter:description', content: 'AI powered platform to create stunning images and organize your collection.' },
        { name: 'og:image', content: 'https://veniceshelf.com/images/logo.png' },
        { name: 'twitter:image', content: 'https://veniceshelf.com/images/logo.png' },
        { name: 'twitter:domain', content: 'veniceshelf.com' },
        { name: 'twitter:url', content: 'https://veniceshelf.com' },
        { name: 'twitter:card', content: 'summary' }
      ],
      link: [{ rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }]
    }
  },

  shadcn: {
    /**
     * Prefix for all the imported component
     */
    prefix: '',
    /**
     * Directory that the component lives in.
     * @default "./components/ui"
     */
    componentDir: './components/ui'
  },

  unlazy: {
    ssr: false
  }
})
