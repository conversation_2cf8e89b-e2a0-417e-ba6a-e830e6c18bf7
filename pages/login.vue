<script setup lang="ts">
definePageMeta({
  layout: 'middle'
})

const user = useSupabaseUser()
watch(
  user,
  () => {
    if (user.value) {
      return navigateTo('/images')
    }
  },
  { immediate: true }
)

const emailSent = ref(false)
</script>

<template>
  <div v-if="!emailSent" class="flex min-h-svh flex-col items-center justify-center gap-6 p-6 md:p-10">
    <div class="flex w-full max-w-sm flex-col gap-6">
      <LoginCard @emailSent="emailSent = true"></LoginCard>
    </div>
  </div>
  <div v-else class="h-full flex items-center justify-center"><h1 class="text-xl">Please check your email to confirm login...</h1></div>
</template>
