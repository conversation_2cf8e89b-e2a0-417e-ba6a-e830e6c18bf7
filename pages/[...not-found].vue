<script setup lang="ts">
definePageMeta({
  layout: 'middle'
})
</script>

<template>
  <main class="grid min-h-full place-items-center px-6 py-24 sm:py-32 lg:px-8">
    <div class="text-center">
      <h4 class="text-4xl font-semibold text-gray-500">404</h4>
      <h1 class="mt-4 text-5xl font-semibold tracking-tight text-balance text-gray-200 sm:text-7xl">Page not found</h1>
      <p class="mt-6 text-lg font-medium text-pretty text-gray-500 sm:text-xl/8">Sorry, we couldn’t find the page you’re looking for.</p>
      <div class="mt-10 flex items-center justify-center gap-x-6">
        <Button as-child variant="outline" size="lg">
          <NuxtLink to="/">Go back home</NuxtLink>
        </Button>
      </div>
    </div>
  </main>
</template>
