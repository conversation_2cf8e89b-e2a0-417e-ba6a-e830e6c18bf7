<script setup lang="ts">
definePageMeta({
  layout: 'middle'
})

const { data: text } = await useAsyncData(() => queryCollection('content').path('/terms').first())

useSeoMeta({
  title: text.value?.title || 'Terms and Conditions',
  description: text.value?.description
})
</script>

<template>
  <ContentRenderer v-if="text" :value="text" class="content space-y-5 my-16" />
  <div v-else>Page not found</div>
</template>

<style scoped>
.content :deep(a) {
  @apply text-pretty underline;
}
</style>
