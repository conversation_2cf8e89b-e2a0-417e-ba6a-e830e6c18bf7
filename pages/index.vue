<script setup lang="ts">
definePageMeta({
  layout: 'full'
})

const config = useRuntimeConfig()
const user = useSupabaseUser()

const { data } = await useFetch('/api/images')
const images = data.value?.data || []

const features = [
  {
    icon: 'lucide:sparkles',
    title: 'AI Image Generation',
    description: 'Create stunning images with advanced AI models. From photorealistic to artistic styles.'
  },
  {
    icon: 'lucide:folder-tree',
    title: 'Smart Organization',
    description: 'Easily categorize and organize your images with intelligent tagging and folders.'
  },
  {
    icon: 'lucide:search',
    title: 'Powerful Search',
    description: 'Find any image instantly with advanced search and filtering capabilities.'
  },
  {
    icon: 'lucide:users',
    title: 'Community Sharing',
    description: 'Share your creations with the community and discover amazing artwork from others.'
  }
]

const steps = [
  {
    number: '01',
    title: 'Generate',
    description: 'Describe your vision and let AI create stunning images for you.'
  },
  {
    number: '02',
    title: 'Organize',
    description: 'Easily categorize and tag your images for easy management.'
  },
  {
    number: '03',
    title: 'Share',
    description: 'Showcase your creations and discover inspiring artwork from the community.'
  }
]
</script>

<template>
  <div class="min-h-screen text-white">
    <!-- Hero Section -->
    <section class="relative overflow-hidden">
      <AppNavbar class="w-full max-w-[1200px] mx-auto z-100 relative" />

      <!-- Background gradient with animation -->
      <div class="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-blue-900/20 to-indigo-900/20"></div>
      <div
        class="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-purple-600/10 via-transparent to-transparent animate-pulse"
      ></div>

      <!-- Floating particles effect -->
      <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-1/4 left-1/4 w-2 h-2 bg-purple-400/30 rounded-full animate-float"></div>
        <div class="absolute top-1/3 right-1/4 w-1 h-1 bg-blue-400/40 rounded-full animate-float" style="animation-delay: 2s"></div>
        <div class="absolute bottom-1/3 left-1/3 w-1.5 h-1.5 bg-indigo-400/30 rounded-full animate-float" style="animation-delay: 4s"></div>
        <div class="absolute top-1/2 right-1/3 w-1 h-1 bg-purple-300/40 rounded-full animate-float" style="animation-delay: 1s"></div>
      </div>

      <div class="relative px-6 py-24 sm:py-32 lg:px-8">
        <div class="mx-auto max-w-5xl text-center">
          <h1
            class="text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl bg-gradient-to-r from-white via-purple-200 to-blue-200 bg-clip-text text-transparent animate-fade-in-up animate-gradient"
          >
            Create. Organize. Discover.
          </h1>
          <p class="mt-6 text-lg leading-8 text-gray-300 sm:text-xl max-w-2xl mx-auto animate-fade-in-up-delay">
            An AI image generation platform to create stunning visuals, organize your collection, and connect with a community of digital artists.
          </p>
          <div v-if="user && !user.is_anonymous" class="mt-10 flex items-center justify-center gap-x-6 animate-fade-in-up-delay-2">
            <Button as-child variant="main" size="lg">
              <NuxtLink :to="user ? '/images' : '/login'">
                {{ user ? 'Open App' : 'Get Started' }}
              </NuxtLink>
            </Button>
            <Button as-child variant="ghost" size="lg" class="px-2">
              <a href="#features" class="text-sm font-semibold leading-6 text-gray-300 hover:text-white transition-colors">
                Learn more <span aria-hidden="true">→</span>
              </a>
            </Button>
          </div>
          <div v-else class="mt-10 animate-fade-in-up-delay-2">
            <HomePromptPanel></HomePromptPanel>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-24 sm:py-32">
      <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <div class="mx-auto max-w-2xl text-center">
          <h2 class="text-3xl font-bold tracking-tight sm:text-4xl bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
            Everything you need to create amazing AI art
          </h2>
          <p class="mt-6 text-lg leading-8 text-gray-400">Powerful tools designed to enhance your creative workflow from generation to organization.</p>
        </div>
        <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
          <dl class="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-4">
            <div v-for="feature in features" :key="feature.title" class="flex flex-col items-center text-center group">
              <div
                class="mb-6 flex h-16 w-16 items-center justify-center rounded-lg bg-gradient-to-br from-purple-600/20 to-blue-600/20 backdrop-blur-sm border border-purple-500/20 group-hover:border-purple-400/40 transition-all duration-300"
              >
                <Icon :name="feature.icon" class="text-2xl text-purple-400 group-hover:text-purple-300 transition-colors" />
              </div>
              <dt class="text-base font-semibold leading-7 text-white">
                {{ feature.title }}
              </dt>
              <dd class="mt-1 text-base leading-7 text-gray-400">
                {{ feature.description }}
              </dd>
            </div>
          </dl>
        </div>
      </div>
    </section>

    <!-- How it works Section -->
    <section class="py-24 sm:py-32 bg-gradient-to-b from-gray-900/50 to-transparent">
      <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <div class="mx-auto max-w-2xl text-center">
          <h2 class="text-3xl font-bold tracking-tight sm:text-4xl bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">How it works</h2>
          <p class="mt-6 text-lg leading-8 text-gray-400">Get started with AI image generation in three simple steps.</p>
        </div>
        <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
          <div class="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
            <div v-for="(step, index) in steps" :key="step.number" class="flex flex-col items-center text-center relative">
              <div
                class="mb-6 flex font-display h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br from-purple-600/60 to-blue-600/60 text-white font-bold text-lg relative z-10"
              >
                {{ step.number }}
              </div>
              <h3 class="text-xl font-semibold leading-7 text-white mb-2">
                {{ step.title }}
              </h3>
              <p class="text-base leading-7 text-gray-400">
                {{ step.description }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Gallery Preview Section -->
    <section class="py-24 sm:py-32">
      <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <div class="mx-auto max-w-2xl text-center">
          <h2 class="text-3xl font-bold tracking-tight sm:text-4xl bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
            Stunning AI creations
          </h2>
          <p class="mt-6 text-lg leading-8 text-gray-400">See what's possible with our AI image generation tools.</p>
        </div>
        <div class="mx-auto mt-16 max-w-2xl columns-2 md:columns-3 lg:columns-4 gap-3 space-y-3 lg:mx-0 lg:max-w-none lg:grid-cols-3">
          <!-- Placeholder for AI generated images -->
          <div v-for="image in images.slice(0, 8)" :key="image.id" class="group relative">
            <div
              class="w-full overflow-hidden rounded-lg bg-gradient-to-br from-purple-900/20 to-blue-900/20 backdrop-blur-sm border border-purple-500/20 group-hover:border-purple-400/40 transition-all duration-300"
            >
              <div class="flex h-full items-center justify-center">
                <UnLazyImage
                  v-if="image.thumbhash"
                  :thumbhash="image.thumbhash"
                  :src="`${config.public.imageBaseUrl}/${image.location}`"
                  class="rounded object-cover hover:scale-110 duration-125"
                />
                <UnLazyImage v-else :src="`${config.public.imageBaseUrl}/${image.location}`" class="rounded object-cover hover:scale-110 duration-125" />
              </div>
            </div>
          </div>
        </div>
        <div class="mt-16 text-center">
          <Button as-child variant="main" size="lg">
            <NuxtLink :to="user ? '/images' : '/login'">
              <Icon name="lucide:arrow-right" class="h-4 w-4" />
              {{ user ? 'View Your Gallery' : 'Start Creating' }}
            </NuxtLink>
          </Button>
        </div>
      </div>
    </section>

    <!-- Community Section -->
    <section class="py-24 sm:py-32 bg-gradient-to-b from-gray-900/50 to-transparent">
      <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <div class="mx-auto max-w-2xl text-center">
          <h2 class="text-3xl font-bold tracking-tight sm:text-4xl bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
            Join the community
          </h2>
          <p class="mt-6 text-lg leading-8 text-gray-400">
            Connect with fellow AI artists, share your creations, and discover inspiring artwork from around the world.
          </p>
        </div>
        <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-4xl">
          <div class="grid grid-cols-1 gap-x-8 gap-y-8 lg:gap-y-16 lg:grid-cols-2">
            <div
              class="flex flex-col items-center text-center p-8 rounded-lg bg-gradient-to-br from-purple-900/20 to-blue-900/20 backdrop-blur-sm border border-purple-500/20"
            >
              <Icon name="lucide:heart" class="text-2xl text-red-400 mb-4" />
              <h3 class="text-xl font-semibold text-white mb-2">Upvote & Discover</h3>
              <p class="text-gray-400">Support amazing artwork and discover trending creations from the community.</p>
            </div>
            <div
              class="flex flex-col items-center text-center p-8 rounded-lg bg-gradient-to-br from-blue-900/20 to-purple-900/20 backdrop-blur-sm border border-blue-500/20"
            >
              <Icon name="lucide:share-2" class="text-2xl text-blue-400 mb-4" />
              <h3 class="text-xl font-semibold text-white mb-2">Share & Inspire</h3>
              <p class="text-gray-400">Showcase your AI creations and inspire others with your unique artistic vision.</p>
            </div>
          </div>
          <div class="mt-2 text-center">
            <p class="text-sm text-gray-500">Community features coming soon!</p>
            <div class="hidden lg:block mt-8 flex justify-center lg:gap-12">
              <div class="flex items-center gap-2 text-md text-gray-400">
                <Icon name="lucide:users" class="h-4 w-4" />
                <span>Growing community</span>
              </div>
              <div class="flex items-center gap-2 text-md text-gray-400">
                <Icon name="lucide:trending-up" class="h-4 w-4" />
                <span>Trending artwork</span>
              </div>
              <div class="flex items-center gap-2 text-md text-gray-400">
                <Icon name="lucide:award" class="h-4 w-4" />
                <span>Featured artists</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-24 sm:py-32">
      <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <div class="mx-auto max-w-5xl text-center">
          <h2 class="text-2xl font-bold tracking-tight sm:text-4xl bg-gradient-to-r from-white via-purple-200 to-blue-200 bg-clip-text text-transparent">
            Ready to create amazing AI art?
          </h2>
          <p class="mt-6 text-lg leading-8 text-gray-400">Join creators who are already using VeniceShelf to bring their imagination to life.</p>
          <div class="mt-10 flex items-center justify-center gap-x-6">
            <Button as-child variant="main" size="lg">
              <NuxtLink :to="user ? '/images' : '/login'">
                {{ user ? 'Open App' : 'Start Creating Now' }}
              </NuxtLink>
            </Button>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <AppFooter></AppFooter>
  </div>
</template>

<style scoped>
/* Custom animations for landing page */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

.animate-fade-in-up-delay {
  animation: fadeInUp 0.8s ease-out 0.2s forwards;
  opacity: 0;
}

.animate-fade-in-up-delay-2 {
  animation: fadeInUp 0.8s ease-out 0.4s forwards;
  opacity: 0;
}

/* Gradient text animation */
.animate-gradient {
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes glow {
  from {
    box-shadow: 0 0 20px rgba(147, 51, 234, 0.3);
  }
  to {
    box-shadow: 0 0 30px rgba(147, 51, 234, 0.6);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
</style>
