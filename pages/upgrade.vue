<script setup lang="ts">
import subscriptionConfig from '~/config/subscriptions.config'

definePageMeta({
  layout: 'full'
})

const user = useSupabaseUser()

const plans = subscriptionConfig
</script>

<template>
  <div class="min-h-screen text-white">
    <!-- Hero Section -->
    <section class="relative overflow-hidden">
      <AppNavbar class="w-full max-w-[1200px] mx-auto z-100 relative" />

      <!-- Background gradient with animation -->
      <div class="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-blue-900/20 to-indigo-900/20"></div>
      <div
        class="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-purple-600/10 via-transparent to-transparent animate-pulse"
      ></div>

      <!-- Floating particles effect -->
      <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-1/4 left-1/4 w-2 h-2 bg-purple-400/30 rounded-full animate-float"></div>
        <div class="absolute top-1/3 right-1/4 w-1 h-1 bg-blue-400/40 rounded-full animate-float" style="animation-delay: 2s"></div>
        <div class="absolute bottom-1/3 left-1/3 w-1.5 h-1.5 bg-indigo-400/30 rounded-full animate-float" style="animation-delay: 4s"></div>
        <div class="absolute top-1/2 right-1/3 w-1 h-1 bg-purple-300/40 rounded-full animate-float" style="animation-delay: 1s"></div>
      </div>

      <div class="relative px-6 py-24 sm:py-32 lg:px-8">
        <div class="mx-auto max-w-5xl text-center">
          <h1
            class="text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl bg-gradient-to-r from-white via-purple-200 to-blue-200 bg-clip-text text-transparent animate-fade-in-up animate-gradient"
          >
            Choose Your Plan
          </h1>
          <p class="mt-6 text-lg leading-8 text-gray-300 sm:text-xl max-w-2xl mx-auto animate-fade-in-up-delay">
            Select the perfect plan for your AI image generation needs. Start free and upgrade as you grow.
          </p>
        </div>
      </div>
    </section>

    <!-- Pricing Section -->
    <section class="py-24 sm:py-32">
      <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <div class="grid grid-cols-1 gap-8 lg:grid-cols-3">
          <div
            v-for="(plan, name) in subscriptionConfig"
            :key="name"
            class="relative flex flex-col rounded-2xl p-8 transition-all duration-300 hover:scale-105"
            :class="[
              plan.popular
                ? 'bg-gradient-to-br from-purple-900/40 to-blue-900/40 backdrop-blur-sm border-2 border-purple-400/50 shadow-2xl shadow-purple-500/20'
                : 'bg-gradient-to-br from-purple-900/20 to-blue-900/20 backdrop-blur-sm border border-purple-500/20'
            ]"
          >
            <!-- Popular badge -->
            <div
              v-if="plan.popular"
              class="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 py-1 rounded-full text-sm font-medium"
            >
              Most Popular
            </div>

            <!-- Plan header -->
            <div class="text-center">
              <div class="flex justify-center mb-4">
                <div
                  class="flex h-16 w-16 items-center justify-center rounded-lg bg-gradient-to-br from-purple-600/20 to-blue-600/20 backdrop-blur-sm border border-purple-500/20"
                >
                  <Icon :name="plan.icon" class="text-xl text-purple-400" />
                </div>
              </div>
              <h3 class="text-2xl font-bold text-white">{{ plan.name }}</h3>
              <div v-if="plan.enabled" class="mt-4 flex items-baseline justify-center gap-x-2">
                <span class="text-5xl font-bold tracking-tight text-white">${{ plan.price }}</span>
                <span class="text-sm font-semibold leading-6 tracking-wide text-gray-400">month</span>
              </div>
              <div v-else class="mt-4 flex items-baseline justify-center gap-x-2">
                <span class="text-5xl font-bold tracking-tight text-white">Soon</span>
              </div>
              <div class="mt-2">
                <span class="text-lg font-medium text-purple-400">
                  <span v-if="plan.credits">{{ `${plan.credits} credits` }}</span>
                  <span v-else class="flex items-center justify-center gap-2"><Icon name="lucide:infinity" class="text-3xl" /> credits</span>
                </span>

                <span class="text-sm text-gray-400 block">reset daily</span>
              </div>
              <p class="mt-4 text-sm leading-6 text-gray-400">{{ plan.description }}</p>
            </div>

            <!-- Features list -->
            <ul class="grow mt-8 space-y-3 text-sm leading-6 text-gray-300">
              <li v-for="feature in plan.features" :key="feature" class="flex gap-x-3">
                <Icon name="lucide:check" class="h-5 w-5 flex-none text-purple-400" />
                {{ feature }}
              </li>
            </ul>

            <!-- CTA Button -->
            <div class="mt-8">
              <Button :variant="plan.popular ? 'main' : 'outline'" :disabled="!plan.enabled" class="w-full" size="lg">
                {{ plan.enabled ? 'Current Plan' : 'Coming Soon' }}
              </Button>
            </div>
          </div>
        </div>

        <!-- Additional info -->
        <div class="mx-auto mt-16 max-w-2xl text-center">
          <p class="text-sm text-gray-500">All plans include access to basic features and support. Premium plans coming soon!</p>
          <div class="mt-8 flex justify-center gap-8">
            <div class="flex items-center gap-2 text-sm text-gray-400">
              <Icon name="lucide:shield-check" class="h-4 w-4" />
              <span>Secure payments</span>
            </div>
            <div class="flex items-center gap-2 text-sm text-gray-400">
              <Icon name="lucide:refresh-cw" class="h-4 w-4" />
              <span>Pay with Credit Card or Crypto</span>
            </div>
            <div class="flex items-center gap-2 text-sm text-gray-400">
              <Icon name="lucide:headphones" class="h-4 w-4" />
              <span>24/7 support</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <!--
    <section class="py-24 sm:py-32 bg-gradient-to-b from-gray-900/50 to-transparent">
      <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <div class="mx-auto max-w-2xl text-center">
          <h2 class="text-3xl font-bold tracking-tight sm:text-4xl bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
            Frequently asked questions
          </h2>
          <p class="mt-6 text-lg leading-8 text-gray-400">Have questions about our pricing? We're here to help.</p>
        </div>
        <div class="mx-auto mt-16 max-w-4xl">
          <div class="grid grid-cols-1 gap-8 lg:grid-cols-2">
            <div class="space-y-6">
              <div class="p-6 rounded-lg bg-gradient-to-br from-purple-900/20 to-blue-900/20 backdrop-blur-sm border border-purple-500/20">
                <h3 class="text-lg font-semibold text-white mb-2">What are credits?</h3>
                <p class="text-gray-400">
                  Credits are used to generate AI images. Each image generation typically uses 1 credit, regardless of the model used.
                </p>
              </div>
              <div class="p-6 rounded-lg bg-gradient-to-br from-blue-900/20 to-purple-900/20 backdrop-blur-sm border border-blue-500/20">
                <h3 class="text-lg font-semibold text-white mb-2">Can I change plans anytime?</h3>
                <p class="text-gray-400">Yes! You can upgrade or downgrade your plan at any time. Changes take effect immediately.</p>
              </div>
            </div>
            <div class="space-y-6">
              <div class="p-6 rounded-lg bg-gradient-to-br from-purple-900/20 to-blue-900/20 backdrop-blur-sm border border-purple-500/20">
                <h3 class="text-lg font-semibold text-white mb-2">What happens to unused credits?</h3>
                <p class="text-gray-400">Credits reset daily, so make sure to use them! We're working on rollover options for premium plans.</p>
              </div>
              <div class="p-6 rounded-lg bg-gradient-to-br from-blue-900/20 to-purple-900/20 backdrop-blur-sm border border-blue-500/20">
                <h3 class="text-lg font-semibold text-white mb-2">When will premium plans be available?</h3>
                <p class="text-gray-400">Premium plans are coming soon! Join our waitlist to be notified when they launch.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    -->

    <!-- Footer -->
    <footer class="border-t border-gray-800">
      <div class="mx-auto max-w-7xl px-6 py-5 lg:px-8">
        <div class="flex flex-col items-center justify-between gap-4 sm:flex-row">
          <AppLogo></AppLogo>
          <div class="flex gap-6">
            <NuxtLink to="/privacy" class="text-xs text-gray-400 hover:text-white transition-colors"> Privacy Policy </NuxtLink>
            <NuxtLink to="/terms" class="text-xs text-gray-400 hover:text-white transition-colors"> Terms of Service </NuxtLink>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<style scoped>
/* Custom animations for landing page */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

.animate-fade-in-up-delay {
  animation: fadeInUp 0.8s ease-out 0.2s forwards;
  opacity: 0;
}

.animate-fade-in-up-delay-2 {
  animation: fadeInUp 0.8s ease-out 0.4s forwards;
  opacity: 0;
}

/* Gradient text animation */
.animate-gradient {
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
</style>
