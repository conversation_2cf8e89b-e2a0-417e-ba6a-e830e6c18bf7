<script setup lang="ts">
import type { RealtimeChannel } from '@supabase/supabase-js'
import { Folder } from 'lucide-vue-next'
import delay from 'delay'

const config = useRuntimeConfig()
const route = useRoute()

const supabase = useSupabaseClient()
const user = useSupabaseUser()

const settingsStore = useSettings()
const foldersStore = useFolders()

const promptPanel = useTemplateRef('prompt-panel')

const imageClass = computed(() => {
  if (settingsStore.settings.view === 'masonry') {
    return { image: 'w-full h-auto', skeleton: 'h-44 w-full aspect-square' }
  }
  if (settingsStore.settings.view === 'compact') {
    return { image: 'size-16 aspect-square', skeleton: 'min-h-16' }
  }

  return { image: 'w-full h-auto lg:size-28 aspect-square', skeleton: 'w-full h-auto lg:min-h-28' }
})

const images: Ref<any[]> = ref([])

const groupsByDate: ComputedRef<{ date: string; images: any[]; selected: number }[]> = computed(() => {
  const groupsByDate = images.value.reduce((acc, image) => {
    const imageDate = new Date(image.created_at).toDateString()
    if (!acc[imageDate]) {
      acc[imageDate] = []
    }
    acc[imageDate].push(image)
    return acc
  }, {} as any)

  return Object.keys(groupsByDate)
    .sort((a, b) => {
      return new Date(b).getTime() - new Date(a).getTime()
    })
    .map((date) => {
      return {
        date: date,
        images: groupsByDate[date].sort((i1: any, i2: any) => i2.created_at.localeCompare(i1.created_at)),
        selected: groupsByDate[date].filter((i: any) => i.selected).length
      }
    })
})

const handleImageEvent = (payload: any) => {
  if (!payload.eventType) return

  if (payload.eventType === 'INSERT') {
    images.value.unshift(payload.new)
    return
  }

  if (payload.eventType === 'UPDATE') {
    const imageIndex = images.value.findIndex((i) => i.id === payload.old.id)
    if (imageIndex === -1) {
      return
    }

    images.value[imageIndex] = payload.new
    return
  }
}

const imageBatchLimit = 50
const isLoadingImages = ref(false)
const loadMoreImages = async ($state: any) => {
  if (isLoadingImages.value) return

  try {
    isLoadingImages.value = true

    const folderId = route.params.folder ? `${route.params.folder}` : null

    let query = supabase
      .from('images')
      .select()
      .in('status', ['pending', 'completed'])
      .order('created_at', { ascending: false })
      .range(images.value.length, images.value.length + imageBatchLimit - 1)
    if (folderId) {
      query = query.eq('folder_id', folderId)
    }

    let { data } = await query
    data = data || []

    // slow down loading to avoid supabase rate limiting
    await delay(125)

    images.value = images.value.concat(data)
    if (data.length < imageBatchLimit) {
      $state.complete()
    } else {
      $state.loaded()
    }
  } catch (error) {
  } finally {
    isLoadingImages.value = false
  }
}

// images modal
const imagesModalOpen = ref(false)
const imagesModalItems = ref([] as any[])
const imagesModalIndex = ref(0)
const imagesModalToggle = (images: any[], selectedImage: any) => {
  imagesModalOpen.value = !imagesModalOpen.value
  imagesModalItems.value = images.filter((image) => image.status === 'completed')
  imagesModalIndex.value = imagesModalItems.value.findIndex((image) => image.id === selectedImage.id) || 0
}

const regenerateImage = (image: any) => {
  promptPanel.value?.triggerGeneration(image)
  imagesModalOpen.value = false
}

// collection actions
const shiftToggleImage = (image: any) => {
  // find group
  const imageDate = new Date(image.created_at).toDateString()
  const group = groupsByDate.value.find((group) => group.date === imageDate)
  if (!group) return

  // find latest selected image
  const shiftClickImageIndex = group.images.findIndex((i) => i.id === image.id)
  if (shiftClickImageIndex === -1) return
  const latestSelectedImage = Math.max(
    0,
    group.images.findLastIndex((image, index) => index < shiftClickImageIndex && image.selected)
  )

  // select all images between latest selected image and current image
  group.images.forEach((image, index) => {
    if (index < latestSelectedImage || index > shiftClickImageIndex) return

    image.selected = true
  })
}
const dragSelectImages = (imageIds: string[], group: any) => {
  const images = []
  for (let imageId of imageIds) {
    const image = group.images.find((i: any) => i.id === imageId)
    if (image) {
      images.push(image)
    }
  }

  // if all images are selected, unselect them
  if (images.length === imageIds.length && images.every((i) => i.selected)) {
    images.forEach((image: any) => {
      image.selected = false
    })
    return
  }

  // otherwise add to selection
  images.forEach((image: any) => {
    image.selected = true
  })
}

const resetSelection = (images: any[]) => {
  images.forEach((image: any) => {
    image.selected = false
  })
}

const isUpdatingCollection = ref(false)
const assignFolder = async (folderId: string, images: any[]) => {
  if (isUpdatingCollection.value) return

  try {
    isUpdatingCollection.value = true

    const imageIds = images.map((i) => i.id)
    await supabase.from('images').update({ folder_id: folderId }).in('id', imageIds)

    resetSelection(images)
    $toast.success('Images assigned to folder successfully')
  } catch (error) {
    $toast.error('Error assigning images to folder, please try again later.')
  } finally {
    isUpdatingCollection.value = false
  }
}

const deleteImages = async (images: any[]) => {
  if (isUpdatingCollection.value) return

  try {
    isUpdatingCollection.value = true

    await $fetch(`/api/images/delete`, {
      method: 'post',
      body: {
        images: images.map((i) => i.id)
      }
    })

    for (let image of images) {
      removeDeletedImage(image.id)
    }

    resetSelection(images)
    $toast.success('Images deleted successfully')
  } catch (error) {
    $toast.error('Error deleting images, please try again later.')
  } finally {
    isUpdatingCollection.value = false
  }
}

const removeDeletedImage = (imageId: string) => {
  // remove from modal
  if (imagesModalOpen.value) {
    imagesModalItems.value = imagesModalItems.value.filter((i) => i.id !== imageId)
    if (imagesModalIndex.value >= imagesModalItems.value.length) {
      imagesModalIndex.value = imagesModalItems.value.length - 1
    }

    // close if empty
    if (!imagesModalItems.value.length) {
      imagesModalOpen.value = false
    }
  }

  // remove from page
  images.value = images.value.filter((i) => i.id !== imageId)
}

// login modal for guest users
const loginModalOpen = ref(false)
watch(
  user,
  () => {
    if (user.value && user.value.is_anonymous) {
      loginModalOpen.value = true
    }
  },
  { immediate: true }
)

// Once page is mounted, listen to changes on the `images` table
let realtimeChannel: RealtimeChannel
onMounted(() => {
  // Real time listener for new changes
  realtimeChannel = supabase.channel('public:images').on('postgres_changes', { event: '*', schema: 'public', table: 'images' }, (payload) => {
    console.log('Refresh: ', payload)
    handleImageEvent(payload)
  })
  realtimeChannel.subscribe()
})
// Don't forget to unsubscribe when user left the page
onBeforeUnmount(() => {
  supabase.removeChannel(realtimeChannel)
})
</script>

<template>
  <div class="flex flex-1 flex-col gap-4 p-4 pt-0">
    <PromptPanel ref="prompt-panel"></PromptPanel>

    <div v-for="group in groupsByDate" :key="group.date" class="space-y-4">
      <div
        class="after:border-border relative flex justify-between items-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t"
      >
        <span class="bg-card text-muted-foreground relative z-10 text-xs font-display"> {{ group.date }} </span>

        <div class="bg-card text-muted-foreground relative z-10 flex items-center gap-2 border border-input rounded-md">
          <TooltipProvider v-if="group.selected">
            <Tooltip>
              <TooltipTrigger
                ><span class="text-xs ml-3 font-display text-emerald-400"> {{ group.selected }} <Icon name="lucide:images" /></span
              ></TooltipTrigger>
              <TooltipContent :sideOffset="0">
                <p>{{ group.selected }} image{{ group.selected === 1 ? '' : 's' }} selected</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <DropdownMenu>
            <DropdownMenuTrigger as-child :disabled="isUpdatingCollection || !group.selected">
              <Button variant="ghost" size="sm"><Icon name="lucide:folder" class="h-4 w-4" /></Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent class="w-44">
              <DropdownMenuItem
                v-for="folder in foldersStore.folders"
                :key="folder.id"
                @click="
                  assignFolder(
                    folder.id,
                    group.images.filter((i) => i.selected)
                  )
                "
                class="py-2 px-4"
              >
                <Folder :class="folder.color || ''" />
                <span>{{ folder.name }}</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Button variant="ghost" size="sm" @click="deleteImages(group.images.filter((i) => i.selected))" :disabled="isUpdatingCollection || !group.selected"
            ><Icon name="lucide:trash" class="text-destructive-foreground h-4 w-4"
          /></Button>
        </div>
      </div>

      <div
        class="select-none"
        :class="
          settingsStore.settings.view === 'masonry'
            ? 'columns-2 md:columns-4 lg:columns-5 gap-2 space-y-2'
            : settingsStore.settings.view === 'compact'
            ? 'grid grid-cols-5 lg:flex lg:flex-wrap gap-2'
            : 'grid grid-cols-3 lg:flex lg:flex-wrap gap-2'
        "
        v-drag-select="{ getSelectedItems: (imageIds: string[]) => dragSelectImages(imageIds, group) }"
      >
        <div v-for="image in group.images" :key="image.id" class="relative">
          <Skeleton v-if="image.status === 'pending'" class="rounded object-cover" :class="[imageClass.image, imageClass.skeleton]" />
          <div v-else class="group selectable" :data-id="image.id">
            <a
              @click.exact="imagesModalToggle(group.images, image)"
              @click.stop.ctrl="image.selected = !image.selected"
              @click.stop.shift="shiftToggleImage(image)"
            >
              <UnLazyImage
                :key="image.thumbhash ? `${image.id}-${image.thumbhash}` : image.id"
                :thumbhash="image.thumbhash"
                :src="`${config.public.imageBaseUrl}/${image.location}`"
                class="rounded object-cover group-hover:scale-105 duration-115"
                :class="[imageClass.image, image.selected ? 'ring-2 ring-primary' : '']"
              />
            </a>
            <Checkbox
              @click.stop
              @click.stop.shift="shiftToggleImage(image)"
              v-model="image.selected"
              class="absolute top-1 left-1 z-10 size-4 hover:backdrop-blur-xs hover:bg-white/20 hover:border-white"
              :disabled="isUpdatingCollection"
            />
          </div>
        </div>
      </div>
    </div>

    <InfiniteLoading @infinite="($state) => loadMoreImages($state)" class="w-full mt-4" />

    <Dialog :open="imagesModalOpen" @update:open="imagesModalOpen = $event">
      <DialogContent class="w-full lg:max-w-7xl p-3 lg:p-6">
        <ImageCarousel
          :images="imagesModalItems"
          :index="imagesModalIndex"
          @regenerate="(image: any) => regenerateImage(image)"
          @deleted="removeDeletedImage"
          class="w-full"
        />

        <DialogFooter>
          <Button @click="imagesModalOpen = false" variant="outline"> Close </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <Dialog :open="loginModalOpen" :close="() => (loginModalOpen = false)">
      <DialogContent class="w-full max-w-sm">
        <LoginCard class="border-0"></LoginCard>
      </DialogContent>
    </Dialog>
  </div>
</template>
