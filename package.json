{"name": "veniceshelf", "version": "0.3.0", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@aws-sdk/client-s3": "^3.825.0", "@aws-sdk/lib-storage": "^3.825.0", "@nuxt/content": "3.5.1", "@nuxt/eslint": "1.4.1", "@nuxt/fonts": "0.11.4", "@nuxt/icon": "1.13.0", "@nuxt/image": "1.10.0", "@nuxt/ui": "3.1.3", "@nuxtjs/supabase": "1.5.1", "@pinia/nuxt": "^0.11.0", "@supabase/supabase-js": "^2.49.8", "@tailwindcss/vite": "^4.1.8", "@vite-pwa/nuxt": "1.0.4", "@vueuse/core": "^13.3.0", "blurhash": "^2.0.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "delay": "^6.0.0", "embla-carousel-vue": "^8.6.0", "eslint": "^9.0.0", "imagescript": "^1.3.1", "lucide-vue-next": "^0.511.0", "nuxt": "^3.17.4", "p-queue": "^8.1.0", "p-retry": "^6.2.1", "p-throttle": "^7.0.0", "p-timeout": "^6.1.4", "pinia": "^3.0.2", "reka-ui": "^2.3.0", "sharp": "^0.34.2", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.8", "thumbhash": "^0.1.1", "tw-animate-css": "^1.3.2", "typescript": "^5.6.3", "vaul-vue": "^0.4.1", "vue": "^3.5.15", "vue-router": "^4.5.1", "vue-sonner": "^2.0.0"}, "devDependencies": {"@unlazy/nuxt": "^0.12.4", "shadcn-nuxt": "^2.2.0", "simple-analytics-vue": "^3.0.2"}, "packageManager": "pnpm@9.15.1+sha512.1acb565e6193efbebda772702950469150cf12bcc764262e7587e71d19dc98a423dff9536e57ea44c49bdf790ff694e83c27be5faa23d67e0c033b583be4bfcf"}